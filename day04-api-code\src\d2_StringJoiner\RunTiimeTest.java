package d2_StringJoiner;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 11:01
 **/
public class RunTiimeTest {
    public static void main(String[] args) {
        //getRuntime()    返回当前Java虚拟机运行时对象
        Runtime jre = Runtime.getRuntime();



        //exit()   终止当前运行的虚拟机
//        jre.exit(0);
//        System.out.println("结束");


        //availableProcessors()   获取当前系统CPU的数量
        System.out.println("CPU数量"+jre.availableProcessors());

        //tatalMemory()   获取当前系统总内存
        System.out.println("java虚拟机中的内存问题："+ jre.totalMemory()/1024/1024.0);


        //freeMemory()   获取当前系统空闲内存
        System.out.println("java虚拟机中的空闲内存："+ jre.freeMemory()/1024/1024.0);

        //exec()
        try {
            jre.exec("notepad");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}