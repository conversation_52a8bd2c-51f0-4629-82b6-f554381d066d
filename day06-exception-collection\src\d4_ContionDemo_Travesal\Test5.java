package d4_ContionDemo_Travesal;

import java.util.ArrayList;
import java.util.Iterator;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 13:38
 **/
public class Test5 {
    public static void main(String[] args) {
        //目标 ： 有一种遍历可能出现的并发修改异常问题
        ArrayList<String> list = new ArrayList<String>();
        list.add("java入门");
        list.add("java基础");
        list.add("java就业");
        list.add("java2");
        list.add("java3");
        list.add("java4");
        list.add("入门");
        list.add("人字");

        //迭代器  会报c
        //他觉得  我们会漏删除 提前抛出异常
        //注意1 如果使用迭代器遍历 ，并用集合删除数据，会出现ConcurrentModificationExceptio
        //需要使用 迭代器自己的remove  迭代器会自己-- 做了同步不会出现不同步的问题
        Iterator it = list.iterator();
        while(it.hasNext()){
            String name = (String) it.next();
            if(name.contains("java")){
                it.remove();   //迭代器自己的remove
            }
        }
        System.out.println(list);

        //使用增强For遍历集合：(本质就是迭代器)必须出错，而且无法解决
        //只适合遍历  不删除
        /*ArrayList<String> list3 = new ArrayList<String>();
        list3.add("java入门");
        list3.add("java基础");
        list3.add("java就业");
        list3.add("java2");
        list3.add("java3");
        list3.add("java4");
        list3.add("入门");
        list3.add("人字");

        for(String name : list3){
            if(name.contains("java")){
                list3.remove(name);
            }
        }*/

        //3.lambda表达式遍历
        //也不支持删除
       /* ArrayList<String> list3 = new ArrayList<String>();
        list3.add("java入门");
        list3.add("java基础");
        list3.add("java就业");
        list3.add("java2");
        list3.add("java3");
        list3.add("java4");
        list3.add("入门");
        list3.add("人字");
        list3.forEach(name -> {
            if (name.contains("java")) {
                list3.remove(name);
            }
        });*/


        //如果是arrayList 带索引的集合，我们也可以使用for循环来删除每次退一步或从后向前删除

    }
}