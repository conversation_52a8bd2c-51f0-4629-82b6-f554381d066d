package d5_regex;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 15:27
 **/
public class regexTest1 {
    public static void main(String[] args) {
        System.out.println(checkQQ2("6543135"));
        System.out.println(checkQQ("af5446"));
    }
    //先不用正则表达式解决

    //正则表达式
    public static boolean checkQQ2(String qq){
       return qq !=null && qq.matches("[1-9]\\d{5,}");
    }

    public static boolean checkQQ(String qq){
        if(qq == null || qq.startsWith("0")||qq.length()<=5) return false;

        for(int i = 0 ; i < qq.length() ; i++){
            char ch = qq.charAt(i);
            if(ch<'0' || ch>'9'){
                return false;
            }
        }
        return true;
    }
}