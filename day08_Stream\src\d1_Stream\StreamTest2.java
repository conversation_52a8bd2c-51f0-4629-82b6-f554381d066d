package d1_Stream;

import java.util.*;
import java.util.stream.Stream;

public class StreamTest2 {
    public static void main(String[] args) {
        //目标：获取stream流

        //1.获取集合的stream流   default stream<E> stream()
        Collection<String> List = new ArrayList<>();
        Collections.addAll(List, "a", "b", "c", "d");
        Stream<String> stream = List.stream();
        System.out.println(stream.count());

        //2.获取Map集合的stream流
        Map<String, Integer> map = new HashMap<>();
        //  a、键流
        Stream<String> ks1 = map.keySet().stream();
        //  b、值流
        Stream<Integer> vs2 = map.values().stream();
        //  c、键值流
        Stream<Map.Entry<String, Integer>> entryStream = map.entrySet().stream();



    }
}
