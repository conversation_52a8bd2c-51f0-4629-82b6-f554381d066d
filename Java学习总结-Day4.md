# Java学习总结 - Day4: 内部类、泛型与枚举

## 📌 当日核心学习目标
- 掌握四种内部类的定义和使用方法
- 深入理解泛型的概念和应用场景
- 熟练运用枚举类型进行常量定义
- 理解内部类在GUI编程中的实际应用
- 掌握泛型在集合框架中的使用

## 🎯 主要知识点详解

### 1. 内部类 (Inner Classes)

#### 1.1 成员内部类 (Member Inner Class)
**核心概念：**
- 定义在外部类的成员位置
- 可以访问外部类的所有成员（包括私有）
- 创建内部类对象需要先创建外部类对象

**语法示例：**
```java
public class Outer {
    private String outerName = "外部类";
    private static String staticField = "静态字段";

    // 成员内部类
    public class Inner {
        private String innerName = "内部类";

        public void innerMethod() {
            // 可以直接访问外部类的成员
            System.out.println("访问外部类成员：" + outerName);
            System.out.println("访问静态成员：" + staticField);
            System.out.println("内部类成员：" + innerName);
        }

        public void accessOuter() {
            // 访问外部类的this
            System.out.println("外部类对象：" + Outer.this);
        }
    }

    public void outerMethod() {
        // 外部类访问内部类
        Inner inner = new Inner();
        inner.innerMethod();
    }
}

// 使用示例
public class InnerClassTest {
    public static void main(String[] args) {
        // 创建外部类对象
        Outer outer = new Outer();
        
        // 创建内部类对象
        Outer.Inner inner = outer.new Inner();
        inner.innerMethod();
        
        // 或者直接调用外部类方法
        outer.outerMethod();
    }
}
```

#### 1.2 静态内部类 (Static Inner Class)
**核心概念：**
- 使用static修饰的内部类
- 独立于外部类对象存在
- 只能访问外部类的静态成员

**语法示例：**
```java
public class Outer {
    private String outerName = "外部类";
    private static String staticField = "静态字段";

    // 静态内部类
    public static class StaticInner {
        private String innerName = "静态内部类";

        public void innerMethod() {
            // 只能访问外部类的静态成员
            System.out.println("访问静态成员：" + staticField);
            // System.out.println(outerName);  // 编译错误
            System.out.println("内部类成员：" + innerName);
        }
    }
}

// 使用示例
public class StaticInnerTest {
    public static void main(String[] args) {
        // 直接创建静态内部类对象，无需外部类对象
        Outer.StaticInner staticInner = new Outer.StaticInner();
        staticInner.innerMethod();
    }
}
```

#### 1.3 局部内部类 (Local Inner Class)
**核心概念：**
- 定义在方法内部的类
- 只能在定义它的方法内使用
- 可以访问外部类成员和方法的final或effectively final变量

**语法示例：**
```java
public class Outer {
    private String outerField = "外部类字段";

    public void outerMethod() {
        String localVar = "局部变量";  // effectively final
        final int count = 10;

        // 局部内部类
        class LocalInner {
            public void innerMethod() {
                System.out.println("访问外部类字段：" + outerField);
                System.out.println("访问局部变量：" + localVar);
                System.out.println("访问final变量：" + count);
            }
        }

        // 在方法内使用局部内部类
        LocalInner localInner = new LocalInner();
        localInner.innerMethod();
    }
}
```

#### 1.4 匿名内部类 (Anonymous Inner Class)
**核心概念：**
- 没有名字的内部类
- 通常用于实现接口或继承类的临时实现
- 常用于事件处理、回调函数等场景

**GUI事件处理应用（基于实际项目代码Test3.java）：**
```java
import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class Test3 {
    public static void main(String[] args) {
        // 掌握匿名内部类的真实场景
        
        // GUI SWING编程，桌面编程
        // 1.创建一个窗口
        JFrame win = new JFrame("登录一下");

        JPanel panel = new JPanel();
        win.add(panel);

        JButton btn = new JButton("登录");
        panel.add(btn);

        // 给按钮绑定单击事件监听器对象，可以用来监听用户的点击，以便做出对应的内容
        // 匿名内部类是作为一个对象 参数传输给方法使用，至于什么时候用，只有方法强制才需要使用
        // 最重要的作用： 简化代码，新技术的基础
        btn.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                System.out.println("点击我");
                JOptionPane.showMessageDialog(win,"别来");
            }
        });

        win.setSize(400,300);
        win.setLocationRelativeTo(null);
        win.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        win.setVisible(true);
    }
}
```

**匿名内部类的核心作用：**
- **简化代码**：避免创建单独的实现类
- **新技术基础**：为Lambda表达式等新特性奠定基础
- **参数传递**：作为对象参数传递给方法使用
- **事件处理**：GUI编程中的事件监听器实现

**实现接口的匿名内部类：**
```java
public interface Runnable {
    void run();
}

public class AnonymousDemo {
    public static void main(String[] args) {
        // 传统方式：创建实现类
        class MyRunnable implements Runnable {
            @Override
            public void run() {
                System.out.println("传统实现方式");
            }
        }
        Runnable r1 = new MyRunnable();

        // 匿名内部类方式
        Runnable r2 = new Runnable() {
            @Override
            public void run() {
                System.out.println("匿名内部类实现");
            }
        };

        r1.run();
        r2.run();
    }
}
```

### 2. 泛型 (Generics)

#### 2.1 泛型基础概念
**核心概念：**
- 泛型是JDK5引入的特性，用于在编译时提供类型安全
- 允许在定义类、接口和方法时使用类型参数
- 避免类型转换，提高代码安全性和可读性

**基本语法：**
```java
// 泛型类
public class Box<T> {
    private T content;

    public void setContent(T content) {
        this.content = content;
    }

    public T getContent() {
        return content;
    }
}

// 使用泛型类
Box<String> stringBox = new Box<>();
stringBox.setContent("Hello");
String content = stringBox.getContent(); // 无需类型转换
```

#### 2.2 泛型集合应用
**常用泛型集合：**
```java
// ArrayList泛型
ArrayList<String> names = new ArrayList<>();
names.add("张三");
names.add("李四");
// names.add(123); // 编译错误，类型不匹配

// HashMap泛型
HashMap<String, Integer> scores = new HashMap<>();
scores.put("张三", 95);
scores.put("李四", 87);

// 遍历泛型集合
for (String name : names) {
    System.out.println(name); // 无需类型转换
}
```

#### 2.3 泛型方法
**语法格式：**
```java
public class GenericMethod {
    // 泛型方法
    public static <T> void swap(T[] array, int i, int j) {
        T temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    // 有界泛型方法
    public static <T extends Number> double sum(T[] numbers) {
        double total = 0.0;
        for (T num : numbers) {
            total += num.doubleValue();
        }
        return total;
    }

    public static void main(String[] args) {
        String[] names = {"Alice", "Bob", "Charlie"};
        swap(names, 0, 2);
        System.out.println(Arrays.toString(names));

        Integer[] nums = {1, 2, 3, 4, 5};
        double result = sum(nums);
        System.out.println("总和：" + result);
    }
}
```

#### 2.4 泛型通配符
**通配符类型：**
```java
public class WildcardDemo {
    // 无界通配符
    public static void printList(List<?> list) {
        for (Object item : list) {
            System.out.println(item);
        }
    }

    // 上界通配符
    public static double sumNumbers(List<? extends Number> numbers) {
        double sum = 0.0;
        for (Number num : numbers) {
            sum += num.doubleValue();
        }
        return sum;
    }

    // 下界通配符
    public static void addNumbers(List<? super Integer> list) {
        list.add(1);
        list.add(2);
        list.add(3);
    }

    public static void main(String[] args) {
        List<String> stringList = Arrays.asList("A", "B", "C");
        List<Integer> intList = Arrays.asList(1, 2, 3);
        List<Double> doubleList = Arrays.asList(1.1, 2.2, 3.3);

        printList(stringList);
        printList(intList);

        System.out.println("整数总和：" + sumNumbers(intList));
        System.out.println("小数总和：" + sumNumbers(doubleList));

        List<Number> numberList = new ArrayList<>();
        addNumbers(numberList);
        System.out.println("添加后的列表：" + numberList);
    }
}
```

### 3. 枚举 (Enum)

#### 3.1 枚举基础概念
**核心概念：**
- 枚举是一种特殊的类，用于定义常量集合
- 使用enum关键字定义
- 枚举值是该枚举类型的实例

**基本语法：**
```java
// 简单枚举
public enum Season {
    SPRING, SUMMER, AUTUMN, WINTER
}

// 使用枚举
public class SeasonTest {
    public static void main(String[] args) {
        Season season = Season.SPRING;
        System.out.println("当前季节：" + season);

        // 枚举比较
        if (season == Season.SPRING) {
            System.out.println("春天来了！");
        }

        // 遍历所有枚举值
        for (Season s : Season.values()) {
            System.out.println(s + " - " + s.ordinal());
        }
    }
}
```

#### 3.2 复杂枚举设计
**带构造器和方法的枚举：**
```java
public enum Planet {
    MERCURY(3.303e+23, 2.4397e6),
    VENUS(4.869e+24, 6.0518e6),
    EARTH(5.976e+24, 6.37814e6),
    MARS(6.421e+23, 3.3972e6);

    private final double mass;   // 质量（千克）
    private final double radius; // 半径（米）

    // 枚举构造器
    Planet(double mass, double radius) {
        this.mass = mass;
        this.radius = radius;
    }

    // 枚举方法
    public double getMass() {
        return mass;
    }

    public double getRadius() {
        return radius;
    }

    // 计算表面重力
    public double surfaceGravity() {
        final double G = 6.67300E-11;
        return G * mass / (radius * radius);
    }

    // 计算物体在该星球上的重量
    public double surfaceWeight(double otherMass) {
        return otherMass * surfaceGravity();
    }
}

// 使用示例
public class PlanetTest {
    public static void main(String[] args) {
        double earthWeight = 70.0; // 地球上的重量（千克）
        
        for (Planet planet : Planet.values()) {
            double weight = planet.surfaceWeight(earthWeight);
            System.out.printf("在%s上的重量：%.2f kg%n", 
                            planet, weight);
        }
    }
}
```

#### 3.3 枚举实现接口
**枚举实现接口示例：**
```java
public interface Describable {
    String getDescription();
}

public enum Color implements Describable {
    RED("红色，热情的颜色"),
    GREEN("绿色，自然的颜色"),
    BLUE("蓝色，宁静的颜色");

    private final String description;

    Color(String description) {
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    // 枚举特有方法
    public boolean isWarmColor() {
        return this == RED;
    }
}

// 使用示例
public class ColorTest {
    public static void main(String[] args) {
        for (Color color : Color.values()) {
            System.out.println(color + ": " + color.getDescription());
            System.out.println("是暖色调：" + color.isWarmColor());
            System.out.println("---");
        }
    }
}
```

## 💻 实际代码示例

### 综合应用：学生管理系统
```java
// 学生状态枚举
public enum StudentStatus {
    ACTIVE("在读"),
    GRADUATED("已毕业"),
    SUSPENDED("休学"),
    EXPELLED("退学");

    private final String description;

    StudentStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

// 泛型学生类
public class Student<T> {
    private String name;
    private T id;  // 泛型ID，可以是String或Integer
    private StudentStatus status;

    public Student(String name, T id, StudentStatus status) {
        this.name = name;
        this.id = id;
        this.status = status;
    }

    // getter和setter方法
    public String getName() { return name; }
    public T getId() { return id; }
    public StudentStatus getStatus() { return status; }
    public void setStatus(StudentStatus status) { this.status = status; }

    @Override
    public String toString() {
        return String.format("Student{name='%s', id=%s, status=%s}", 
                           name, id, status.getDescription());
    }
}

// 学生管理器
public class StudentManager<T> {
    private List<Student<T>> students;

    public StudentManager() {
        this.students = new ArrayList<>();
    }

    public void addStudent(Student<T> student) {
        students.add(student);
    }

    public List<Student<T>> getStudentsByStatus(StudentStatus status) {
        List<Student<T>> result = new ArrayList<>();
        for (Student<T> student : students) {
            if (student.getStatus() == status) {
                result.add(student);
            }
        }
        return result;
    }

    public void printAllStudents() {
        for (Student<T> student : students) {
            System.out.println(student);
        }
    }

    // 内部类：统计信息
    public class Statistics {
        public void printStatusStatistics() {
            Map<StudentStatus, Integer> statusCount = new HashMap<>();
            
            for (Student<T> student : students) {
                StudentStatus status = student.getStatus();
                statusCount.put(status, statusCount.getOrDefault(status, 0) + 1);
            }
            
            System.out.println("学生状态统计：");
            for (Map.Entry<StudentStatus, Integer> entry : statusCount.entrySet()) {
                System.out.println(entry.getKey().getDescription() + ": " + entry.getValue());
            }
        }
    }
}

// 测试类
public class StudentTest {
    public static void main(String[] args) {
        StudentManager<String> manager = new StudentManager<>();
        
        // 添加学生
        manager.addStudent(new Student<>("张三", "S001", StudentStatus.ACTIVE));
        manager.addStudent(new Student<>("李四", "S002", StudentStatus.GRADUATED));
        manager.addStudent(new Student<>("王五", "S003", StudentStatus.ACTIVE));
        
        // 打印所有学生
        manager.printAllStudents();
        
        // 获取在读学生
        List<Student<String>> activeStudents = manager.getStudentsByStatus(StudentStatus.ACTIVE);
        System.out.println("\n在读学生：");
        for (Student<String> student : activeStudents) {
            System.out.println(student);
        }
        
        // 使用内部类统计
        StudentManager<String>.Statistics stats = manager.new Statistics();
        stats.printStatusStatistics();
    }
}
```

## 🔍 重点难点分析

### 1. 内部类的访问规则
- **成员内部类**：可以访问外部类的所有成员
- **静态内部类**：只能访问外部类的静态成员
- **局部内部类**：可以访问外部类成员和方法的final变量
- **匿名内部类**：常用于接口实现和事件处理

### 2. 泛型的类型擦除
- **编译时检查**：泛型信息在编译时进行类型检查
- **运行时擦除**：运行时泛型信息被擦除，保持向后兼容
- **类型安全**：避免ClassCastException，提高代码安全性

### 3. 枚举的特殊性
- **类型安全**：编译时检查，避免使用无效常量
- **单例保证**：每个枚举值都是单例
- **功能丰富**：可以有构造器、方法、字段

## 📝 当日学习总结和要点回顾

### 🎯 内部类要点
1. **成员内部类**：可访问外部类所有成员，需要外部类对象才能创建
2. **静态内部类**：独立于外部类对象，只能访问外部类静态成员
3. **局部内部类**：定义在方法内，只能访问final或effectively final变量
4. **匿名内部类**：没有名字，常用于接口实现和事件处理
5. **应用场景**：GUI事件处理、回调函数、辅助类实现

### 🎯 泛型要点
1. **类型安全**：编译时检查类型，避免ClassCastException
2. **消除转换**：无需显式类型转换，提高代码可读性
3. **代码复用**：一套代码处理多种类型
4. **通配符**：? extends（上界）、? super（下界）、?（无界）
5. **类型擦除**：运行时泛型信息被擦除，保持向后兼容

### 🎯 枚举要点
1. **类型安全**：编译时检查，避免使用无效常量
2. **单例保证**：每个枚举值都是单例
3. **功能丰富**：可以有构造器、方法、字段
4. **接口实现**：枚举可以实现接口
5. **应用场景**：状态机、配置选项、常量定义

### 🎯 设计模式应用
- **单例模式**：枚举实现单例（最佳实践）
- **策略模式**：枚举实现不同策略
- **工厂模式**：泛型工厂方法
- **观察者模式**：匿名内部类实现监听器

---

**下一步学习预告：** Day5将学习常用API与字符串处理，掌握Java核心类库的使用方法。
