package d4_ContionDemo_Travesal;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.function.Consumer;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 11:16
 **/
public class CollectionDemo2 {
    public static void main(String[] args) {
        //目标：掌握collection集合的遍历方式  二 : lambda表达式遍历
        //1.准备一个集合
        Collection<String> list = new ArrayList<>();
        list.add("A");
        list.add("B");
        list.add("C");
        System.out.println(list);

       /* //对象回调   将参数传给action 然后 forEach 回调accept方法 打印内容
        list.forEach(new Consumer<String>() {
            @Override
            public void accept(String s) {
                System.out.println(s);
            }
        });*/

        list.forEach(s -> System.out.println(s));
        list.forEach(System.out::println);
    }
}