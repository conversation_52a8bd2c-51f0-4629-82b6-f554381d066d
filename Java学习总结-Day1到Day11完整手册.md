# Java学习总结 - Day1到Day11完整手册

## 📚 学习概览

本文档系统总结了Java SE核心技术的完整学习路径，从面向对象编程基础到高级特性应用，涵盖了Java开发的核心知识体系。

### 🎯 学习目标
- 掌握Java面向对象编程的核心思想和实现机制
- 熟练运用Java常用API和集合框架
- 理解异常处理和IO流操作
- 具备Java项目开发的基础能力

### 📖 知识体系结构
```
Java SE核心技术
├── 面向对象编程基础 (Day1-Day3)
│   ├── Static静态特性与继承机制
│   ├── 多态、抽象类与接口编程
│   └── 内部类、泛型与枚举
├── 常用API与工具类 (Day4-Day5)
│   ├── 字符串处理与时间API
│   ├── Lambda表达式与函数式编程
│   └── 正则表达式与方法引用
├── 集合框架与异常处理 (Day6-Day7)
│   ├── 异常处理机制
│   ├── Collection集合体系
│   └── Map集合与数据结构
├── 流处理与文件操作 (Day8-Day11)
│   ├── Stream流式编程
│   ├── IO流体系
│   ├── 文件操作与NIO
│   └── 特殊文件处理与日志
```

---

## 🔧 Day1: Static静态特性与继承机制

### 📌 核心学习目标
- 理解static关键字的作用机制和应用场景
- 掌握Java继承的实现原理和使用规范
- 熟练运用访问修饰符控制成员访问权限
- 理解方法重写和构造器调用规则

### 🎯 Static静态特性

#### 1.1 静态变量 (Static Variables)
**核心概念：**
- 使用`static`关键字修饰的成员变量
- 在内存中只有一份，被类和所有对象共享
- 随类加载而加载，优先于对象创建

**语法格式：**
```java
public class Student {
    static String schoolName = "黑马程序员";  // 静态变量
    int age;  // 实例变量
}
```

**访问方式：**
```java
// 推荐方式：类名.静态变量
Student.schoolName = "新学校名";

// 不推荐：对象名.静态变量
Student s = new Student();
s.schoolName = "新学校名";  // 不推荐
```

**关键特点：**
- 内存共享：所有对象共享同一份静态变量
- 类级别：属于类，不属于任何具体对象
- 生命周期：随类加载创建，随类卸载销毁

#### 1.2 静态方法 (Static Methods)
**核心概念：**
- 使用`static`关键字修饰的方法
- 可以直接通过类名调用，无需创建对象
- 常用于工具类和通用功能实现

**语法格式：**
```java
public class MathUtil {
    public static int add(int a, int b) {
        return a + b;
    }
}

// 调用方式
int result = MathUtil.add(10, 20);
```

**使用限制：**
```java
public class Demo {
    static String staticVar = "静态变量";
    String instanceVar = "实例变量";
    
    public static void staticMethod() {
        System.out.println(staticVar);      // ✓ 可以访问静态成员
        // System.out.println(instanceVar); // ✗ 不能直接访问实例成员
        // this.instanceVar;                // ✗ 不能使用this关键字
    }
    
    public void instanceMethod() {
        System.out.println(staticVar);      // ✓ 可以访问静态成员
        System.out.println(instanceVar);    // ✓ 可以访问实例成员
        System.out.println(this.instanceVar); // ✓ 可以使用this关键字
    }
}
```

#### 1.3 静态代码块 (Static Code Blocks)
**核心概念：**
- 使用`static {}`语法定义
- 在类加载时自动执行，且只执行一次
- 用于初始化静态资源和执行类级别的初始化操作

**语法格式：**
```java
public class InitDemo {
    public static String config;
    public static List<String> dataList = new ArrayList<>();
    
    // 静态代码块
    static {
        System.out.println("静态代码块执行");
        config = "默认配置";
        dataList.add("初始数据1");
        dataList.add("初始数据2");
    }
    
    public static void main(String[] args) {
        System.out.println("main方法执行");
        System.out.println("配置：" + config);
        System.out.println("数据：" + dataList);
    }
}
```

**执行顺序：**
```
静态代码块 → main方法 → 实例代码块 → 构造器
```

#### 1.4 单例设计模式 (Singleton Pattern)
**设计目的：**
- 确保一个类只有一个实例
- 提供全局访问点
- 节省系统资源

**饿汉式单例（推荐）：**
```java
public class Singleton {
    // 私有静态变量，类加载时创建唯一实例
    private static final Singleton INSTANCE = new Singleton();
    
    // 私有构造器，防止外部实例化
    private Singleton() {}
    
    // 公共静态方法，提供全局访问点
    public static Singleton getInstance() {
        return INSTANCE;
    }
}
```

**懒汉式单例：**
```java
public class LazySingleton {
    private static LazySingleton instance;
    
    private LazySingleton() {}
    
    public static LazySingleton getInstance() {
        if (instance == null) {
            instance = new LazySingleton();
        }
        return instance;
    }
}
```

**对比分析：**
| 特性 | 饿汉式 | 懒汉式 |
|------|--------|--------|
| 创建时机 | 类加载时 | 第一次使用时 |
| 线程安全 | 天然线程安全 | 需要同步处理 |
| 内存占用 | 可能浪费内存 | 节省内存 |
| 性能 | 无同步开销 | 有同步开销 |

### 🏗️ 继承机制 (Inheritance)

#### 2.1 基础继承概念
**核心概念：**
- 使用`extends`关键字实现类的继承关系
- 子类自动获得父类的非私有成员
- 实现代码复用和建立类的层次结构

**语法格式：**
```java
// 父类
public class Animal {
    protected String name;
    private int age;  // 私有成员，子类不能直接访问
    
    public Animal(String name) {
        this.name = name;
    }
    
    public void eat() {
        System.out.println(name + "在吃东西");
    }
    
    protected void sleep() {
        System.out.println(name + "在睡觉");
    }
}

// 子类
public class Dog extends Animal {
    private String breed;
    
    public Dog(String name, String breed) {
        super(name);  // 调用父类构造器
        this.breed = breed;
    }
    
    public void bark() {
        System.out.println(name + "在汪汪叫");  // 可以访问protected成员
    }
    
    @Override
    public void eat() {
        System.out.println(name + "正在吃狗粮");  // 方法重写
    }
}
```

#### 2.2 访问修饰符权限控制
**权限级别（从小到大）：**
```
private < 默认(package) < protected < public
```

**权限控制表：**
| 修饰符 | 本类 | 同包 | 子类 | 任意位置 |
|--------|------|------|------|----------|
| private | ✓ | ✗ | ✗ | ✗ |
| 默认 | ✓ | ✓ | ✗ | ✗ |
| protected | ✓ | ✓ | ✓ | ✗ |
| public | ✓ | ✓ | ✓ | ✓ |

**实际应用示例：**
```java
public class AccessDemo {
    private String privateField = "私有字段";
    String packageField = "包访问字段";
    protected String protectedField = "受保护字段";
    public String publicField = "公共字段";
    
    private void privateMethod() { }
    void packageMethod() { }
    protected void protectedMethod() { }
    public void publicMethod() { }
}
```

#### 2.3 方法重写 (Method Overriding)
**核心概念：**
- 子类重新定义父类的方法实现
- 使用`@Override`注解确保重写正确性
- 为多态机制提供技术基础

**重写规则：**
```java
public class Vehicle {
    public void start() {
        System.out.println("车辆启动");
    }
    
    protected void stop() {
        System.out.println("车辆停止");
    }
}

public class Car extends Vehicle {
    @Override
    public void start() {  // 访问权限不能比父类更严格
        System.out.println("汽车点火启动");
    }
    
    @Override
    public void stop() {  // 可以扩大访问权限
        System.out.println("汽车刹车停止");
    }
}
```

**重写约束：**
1. 方法名、参数列表、返回类型必须相同
2. 访问权限不能比父类更严格
3. 不能重写private、static、final方法
4. 抛出的异常不能比父类更宽泛

#### 2.4 构造器调用机制
**核心原理：**
- 子类构造器必须先调用父类构造器
- 默认调用父类无参构造器
- 可以使用`super()`显式调用父类构造器

**调用示例：**
```java
public class Person {
    private String name;
    private int age;
    
    public Person() {
        System.out.println("Person无参构造器");
    }
    
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
        System.out.println("Person有参构造器");
    }
}

public class Student extends Person {
    private String school;
    
    public Student() {
        super();  // 显式调用父类无参构造器（可省略）
        System.out.println("Student无参构造器");
    }
    
    public Student(String name, int age, String school) {
        super(name, age);  // 调用父类有参构造器
        this.school = school;
        System.out.println("Student有参构造器");
    }
}
```

**执行顺序：**
```
父类构造器 → 子类构造器
```

#### 2.5 this和super关键字
**this关键字用法：**
```java
public class ThisDemo {
    private String name;
    
    public ThisDemo(String name) {
        this.name = name;  // 区分参数和成员变量
    }
    
    public ThisDemo() {
        this("默认名称");  // 调用本类其他构造器
    }
    
    public void setName(String name) {
        this.name = name;  // 访问当前对象的成员变量
    }
    
    public void printInfo() {
        this.setName("新名称");  // 调用当前对象的方法
    }
}
```

**super关键字用法：**
```java
public class SuperDemo extends ThisDemo {
    private String title;
    
    public SuperDemo(String name, String title) {
        super(name);  // 调用父类构造器
        this.title = title;
    }
    
    @Override
    public void printInfo() {
        super.printInfo();  // 调用父类方法
        System.out.println("标题：" + title);
    }
}
```

### 📊 Day1核心知识点总结

#### 🎯 Static特性要点
1. **内存特点**：静态成员在内存中只有一份，类级别共享
2. **加载时机**：随类加载而加载，优先于对象创建
3. **访问方式**：推荐使用类名直接访问
4. **应用场景**：工具类、单例模式、共享数据
5. **使用限制**：静态方法只能访问静态成员，不能使用this

#### 🎯 继承机制要点
1. **代码复用**：子类自动获得父类的非私有成员
2. **层次结构**：建立类之间的is-a关系
3. **方法重写**：子类可以重新定义父类方法的行为
4. **构造顺序**：先调用父类构造器，再调用子类构造器
5. **访问控制**：通过修饰符控制继承的范围
6. **成员访问**：遵循就近原则（局部→子类→父类）

#### 🎯 关键字使用总结
- **static**：修饰类级别的成员，实现共享和工具功能
- **extends**：实现类的继承关系
- **@Override**：确保方法重写的正确性
- **super**：访问父类成员和构造器
- **this**：访问当前类成员和构造器

---

## 🚀 Day2: 多态、Final关键字、抽象类与接口编程

### 📌 核心学习目标
- 深入理解多态的实现机制和应用场景
- 掌握final关键字的多种用法和约束规则
- 熟练运用抽象类设计模板方法模式
- 精通接口编程思想和JDK8新特性
- 理解面向对象设计的核心原则

### 🎭 多态机制 (Polymorphism)

#### 3.1 多态基础概念
**核心定义：**
- 同一个引用类型，使用不同的实例而执行不同操作
- 编译看左边，运行看右边
- 实现代码的灵活性和可扩展性

**多态的形式：**
```java
// 父类引用指向子类对象
Animal animal = new Dog();  // 向上转型
animal.eat();  // 调用Dog类重写的eat方法

// 接口引用指向实现类对象
List<String> list = new ArrayList<>();  // 多态应用
```

**多态成立的条件：**
1. 有继承/实现关系
2. 有方法重写
3. 有父类引用指向子类对象

#### 3.2 多态的内存机制
**动态绑定原理：**
```java
public class Animal {
    public void eat() {
        System.out.println("动物在吃东西");
    }

    public void sleep() {
        System.out.println("动物在睡觉");
    }
}

public class Cat extends Animal {
    @Override
    public void eat() {
        System.out.println("猫在吃鱼");
    }

    // 子类特有方法
    public void catchMouse() {
        System.out.println("猫在抓老鼠");
    }
}

public class Dog extends Animal {
    @Override
    public void eat() {
        System.out.println("狗在吃骨头");
    }

    // 子类特有方法
    public void watchHouse() {
        System.out.println("狗在看家");
    }
}
```

**多态调用示例：**
```java
public class PolymorphismDemo {
    public static void main(String[] args) {
        // 多态数组
        Animal[] animals = {
            new Cat(),
            new Dog(),
            new Cat()
        };

        // 统一调用，不同行为
        for (Animal animal : animals) {
            animal.eat();  // 运行时动态绑定到具体子类方法
            animal.sleep();
        }
    }
}
```

**输出结果：**
```
猫在吃鱼
动物在睡觉
狗在吃骨头
动物在睡觉
猫在吃鱼
动物在睡觉
```

#### 3.3 类型转换机制
**向上转型（自动转换）：**
```java
Animal animal = new Cat();  // 自动向上转型
// 优点：代码通用性强
// 缺点：不能调用子类特有方法
```

**向下转型（强制转换）：**
```java
Animal animal = new Cat();

// 强制向下转型
Cat cat = (Cat) animal;
cat.catchMouse();  // 现在可以调用子类特有方法

// 错误的向下转型会导致ClassCastException
// Dog dog = (Dog) animal;  // 运行时异常
```

**instanceof运算符：**
```java
public static void handleAnimal(Animal animal) {
    // 安全的类型判断和转换
    if (animal instanceof Cat) {
        Cat cat = (Cat) animal;
        cat.catchMouse();
    } else if (animal instanceof Dog) {
        Dog dog = (Dog) animal;
        dog.watchHouse();
    }

    // 通用操作
    animal.eat();
}
```

#### 3.4 多态的优势和应用
**代码示例：动物管理系统**
```java
public class AnimalManager {
    // 多态参数：可以接收任何Animal子类对象
    public void feedAnimal(Animal animal) {
        animal.eat();  // 不同动物有不同的吃法
    }

    // 多态数组：统一管理不同类型的动物
    public void feedAllAnimals(Animal[] animals) {
        for (Animal animal : animals) {
            feedAnimal(animal);
        }
    }

    // 多态返回值：根据条件返回不同的子类对象
    public Animal createAnimal(String type) {
        switch (type) {
            case "cat": return new Cat();
            case "dog": return new Dog();
            default: return new Animal();
        }
    }
}
```

**多态的优势：**
1. **提高代码复用性**：一套代码处理多种类型
2. **增强可扩展性**：新增子类无需修改现有代码
3. **降低耦合度**：依赖抽象而非具体实现
4. **提升维护性**：修改子类行为不影响调用方

### 🔒 Final关键字

#### 4.1 Final修饰类
**核心概念：**
- final修饰的类不能被继承
- 常见的final类：String、Integer、LocalDate等

**语法示例：**
```java
// final类不能被继承
public final class FinalClass {
    public void method() {
        System.out.println("final类的方法");
    }
}

// 编译错误：Cannot inherit from final class
// public class SubClass extends FinalClass { }
```

**应用场景：**
- 工具类：如Math、Arrays
- 不可变类：如String、包装类
- 安全敏感类：防止恶意继承

#### 4.2 Final修饰方法
**核心概念：**
- final修饰的方法不能被子类重写
- 保证方法行为的一致性

**语法示例：**
```java
public class Parent {
    // final方法不能被重写
    public final void finalMethod() {
        System.out.println("这是final方法");
    }

    public void normalMethod() {
        System.out.println("普通方法");
    }
}

public class Child extends Parent {
    // 编译错误：Cannot override the final method
    // public void finalMethod() { }

    @Override
    public void normalMethod() {
        System.out.println("重写的普通方法");
    }
}
```

#### 4.3 Final修饰变量
**修饰局部变量：**
```java
public void method() {
    final int x = 10;
    // x = 20;  // 编译错误：Cannot assign a value to final variable

    final List<String> list = new ArrayList<>();
    list.add("元素");  // 可以修改对象内容
    // list = new ArrayList<>();  // 编译错误：不能重新赋值
}
```

**修饰成员变量：**
```java
public class FinalFieldDemo {
    // 方式1：声明时初始化
    private final String name = "默认名称";

    // 方式2：构造器中初始化
    private final int age;

    // 方式3：实例代码块中初始化
    private final String address;
    {
        address = "默认地址";
    }

    public FinalFieldDemo(int age) {
        this.age = age;  // 必须在构造器中初始化
    }
}
```

**修饰静态变量（常量）：**
```java
public class Constants {
    // 公共常量：通常使用public static final
    public static final String COMPANY_NAME = "黑马程序员";
    public static final int MAX_SIZE = 100;
    public static final double PI = 3.14159;

    // 私有常量
    private static final String SECRET_KEY = "abc123";
}
```

### 🎨 抽象类 (Abstract Classes)

#### 5.1 抽象类基础概念
**核心定义：**
- 使用`abstract`关键字修饰的类
- 不能被实例化，只能被继承
- 可以包含抽象方法和具体方法
- 为子类提供通用的模板和规范

**语法格式：**
```java
public abstract class Shape {
    protected String color;

    // 构造器
    public Shape(String color) {
        this.color = color;
    }

    // 具体方法
    public void setColor(String color) {
        this.color = color;
    }

    // 抽象方法：子类必须实现
    public abstract double calculateArea();
    public abstract double calculatePerimeter();

    // 模板方法：定义算法骨架
    public final void printInfo() {
        System.out.println("颜色：" + color);
        System.out.println("面积：" + calculateArea());
        System.out.println("周长：" + calculatePerimeter());
    }
}
```

#### 5.2 抽象方法
**核心特点：**
- 使用`abstract`关键字修饰
- 只有方法声明，没有方法体
- 子类必须重写所有抽象方法

**实现示例：**
```java
// 圆形类
public class Circle extends Shape {
    private double radius;

    public Circle(String color, double radius) {
        super(color);
        this.radius = radius;
    }

    @Override
    public double calculateArea() {
        return Math.PI * radius * radius;
    }

    @Override
    public double calculatePerimeter() {
        return 2 * Math.PI * radius;
    }
}

// 矩形类
public class Rectangle extends Shape {
    private double width;
    private double height;

    public Rectangle(String color, double width, double height) {
        super(color);
        this.width = width;
        this.height = height;
    }

    @Override
    public double calculateArea() {
        return width * height;
    }

    @Override
    public double calculatePerimeter() {
        return 2 * (width + height);
    }
}
```

#### 5.3 模板方法模式
**设计思想：**
- 在抽象类中定义算法的骨架
- 将一些步骤延迟到子类中实现
- 实现代码复用和行为规范

**实际应用示例：**
```java
public abstract class DataProcessor {
    // 模板方法：定义数据处理流程
    public final void processData() {
        loadData();
        validateData();
        transformData();
        saveData();
        cleanup();
    }

    // 具体方法：通用逻辑
    private void loadData() {
        System.out.println("加载数据...");
    }

    private void cleanup() {
        System.out.println("清理资源...");
    }

    // 抽象方法：子类实现具体逻辑
    protected abstract void validateData();
    protected abstract void transformData();
    protected abstract void saveData();
}

// 具体实现类
public class XMLDataProcessor extends DataProcessor {
    @Override
    protected void validateData() {
        System.out.println("验证XML数据格式");
    }

    @Override
    protected void transformData() {
        System.out.println("转换XML数据");
    }

    @Override
    protected void saveData() {
        System.out.println("保存到XML文件");
    }
}
```

### 🔌 接口编程 (Interface Programming)

#### 6.1 接口基础概念
**核心定义：**
- 使用`interface`关键字定义
- 是一种引用数据类型，类似于类
- 定义了一组抽象方法的集合
- 实现类必须实现接口中的所有抽象方法

**JDK8之前的接口特点：**
```java
public interface Animal {
    // 常量：public static final（可省略）
    String KINGDOM = "动物界";
    int MAX_AGE = 200;

    // 抽象方法：public abstract（可省略）
    void eat();
    void sleep();
    void move();
}
```

#### 6.2 接口的实现
**单接口实现：**
```java
public class Dog implements Animal {
    @Override
    public void eat() {
        System.out.println("狗在吃骨头");
    }

    @Override
    public void sleep() {
        System.out.println("狗在睡觉");
    }

    @Override
    public void move() {
        System.out.println("狗在跑步");
    }
}
```

**多接口实现：**
```java
public interface Flyable {
    void fly();
}

public interface Swimmable {
    void swim();
}

// 一个类可以实现多个接口
public class Duck implements Animal, Flyable, Swimmable {
    @Override
    public void eat() {
        System.out.println("鸭子在吃虫子");
    }

    @Override
    public void sleep() {
        System.out.println("鸭子在睡觉");
    }

    @Override
    public void move() {
        System.out.println("鸭子在走路");
    }

    @Override
    public void fly() {
        System.out.println("鸭子在飞翔");
    }

    @Override
    public void swim() {
        System.out.println("鸭子在游泳");
    }
}
```

#### 6.3 接口的继承
**接口间的继承：**
```java
public interface Vehicle {
    void start();
    void stop();
}

public interface Car extends Vehicle {
    void drive();
    void park();
}

public interface ElectricCar extends Car {
    void charge();
    int getBatteryLevel();
}

// 实现类需要实现所有继承链上的方法
public class Tesla implements ElectricCar {
    private int batteryLevel = 100;

    @Override
    public void start() {
        System.out.println("特斯拉启动");
    }

    @Override
    public void stop() {
        System.out.println("特斯拉停止");
    }

    @Override
    public void drive() {
        System.out.println("特斯拉行驶");
    }

    @Override
    public void park() {
        System.out.println("特斯拉停车");
    }

    @Override
    public void charge() {
        System.out.println("特斯拉充电");
        batteryLevel = 100;
    }

    @Override
    public int getBatteryLevel() {
        return batteryLevel;
    }
}
```

#### 6.4 JDK8接口新特性
**默认方法 (Default Methods)：**
```java
public interface Drawable {
    // 抽象方法
    void draw();

    // 默认方法：有方法体，实现类可以选择重写
    default void setColor(String color) {
        System.out.println("设置颜色为：" + color);
    }

    default void reset() {
        System.out.println("重置绘图状态");
    }
}

public class Circle implements Drawable {
    @Override
    public void draw() {
        System.out.println("绘制圆形");
    }

    // 可以选择重写默认方法
    @Override
    public void setColor(String color) {
        System.out.println("圆形颜色设置为：" + color);
    }

    // 也可以不重写，直接使用接口的默认实现
}
```

**静态方法 (Static Methods)：**
```java
public interface MathUtils {
    // 静态方法：属于接口，通过接口名调用
    static int add(int a, int b) {
        return a + b;
    }

    static double calculateCircleArea(double radius) {
        return Math.PI * radius * radius;
    }

    // 私有静态方法（JDK9+）：为其他静态方法提供辅助
    private static void log(String message) {
        System.out.println("Log: " + message);
    }
}

// 调用方式
int result = MathUtils.add(10, 20);
double area = MathUtils.calculateCircleArea(5.0);
```

**接口中的私有方法（JDK9+）：**
```java
public interface Logger {
    default void logInfo(String message) {
        log("INFO", message);
    }

    default void logError(String message) {
        log("ERROR", message);
    }

    // 私有方法：为默认方法提供公共逻辑
    private void log(String level, String message) {
        System.out.println("[" + level + "] " + message);
    }
}
```

### 📊 抽象类 vs 接口对比

| 特性 | 抽象类 | 接口 |
|------|--------|------|
| 关键字 | abstract class | interface |
| 继承/实现 | extends（单继承） | implements（多实现） |
| 构造器 | 可以有 | 不能有 |
| 成员变量 | 任意类型 | public static final |
| 方法类型 | 抽象+具体+静态 | 抽象+默认+静态+私有 |
| 访问修饰符 | 任意 | public（默认） |
| 设计目的 | is-a关系，代码复用 | can-do能力，规范定义 |

**选择原则：**
- **使用抽象类**：当多个类有共同的属性和方法实现时
- **使用接口**：当需要定义一组规范或能力时
- **组合使用**：抽象类实现接口，提供部分默认实现

### 📊 Day2核心知识点总结

#### 🎯 多态机制要点
1. **实现条件**：继承关系 + 方法重写 + 父类引用指向子类对象
2. **动态绑定**：编译时看引用类型，运行时看实际对象类型
3. **类型转换**：向上转型自动，向下转型需要强制转换和类型检查
4. **应用优势**：提高代码复用性、可扩展性和维护性
5. **注意事项**：多态调用不能访问子类特有成员

#### 🎯 Final关键字要点
1. **修饰类**：类不能被继承（如String、Integer）
2. **修饰方法**：方法不能被重写
3. **修饰变量**：变量不能被重新赋值（常量）
4. **应用场景**：工具类、不可变类、安全敏感类
5. **注意事项**：final修饰引用类型时，引用不可变但对象内容可变

#### 🎯 抽象类要点
1. **设计目的**：为子类提供通用模板和规范
2. **核心特点**：不能实例化，可包含抽象和具体方法
3. **模板模式**：定义算法骨架，延迟部分实现到子类
4. **使用场景**：多个类有共同属性和部分共同行为
5. **继承规则**：子类必须实现所有抽象方法

#### 🎯 接口编程要点
1. **设计理念**：定义规范和能力，实现多重继承效果
2. **JDK8新特性**：默认方法、静态方法
3. **多接口实现**：一个类可以实现多个接口
4. **接口继承**：接口可以继承多个接口
5. **应用优势**：降低耦合度，提高代码灵活性

#### 🎯 面向对象设计原则
- **开闭原则**：对扩展开放，对修改关闭
- **里氏替换原则**：子类可以替换父类
- **接口隔离原则**：接口应该小而专一
- **依赖倒置原则**：依赖抽象而非具体实现

---

## 🏗️ Day3: 内部类、泛型与枚举

### 📌 核心学习目标
- 掌握四种内部类的特点和应用场景
- 理解泛型的设计思想和使用方法
- 熟练运用枚举类型解决常量定义问题
- 理解类型安全和代码复用的重要性

### 🏠 内部类 (Inner Classes)

#### 7.1 成员内部类 (Member Inner Class)
**核心概念：**
- 定义在外部类成员位置的类
- 可以访问外部类的所有成员（包括私有）
- 外部类也可以访问内部类的私有成员
- 内部类对象依赖于外部类对象存在

**语法格式：**
```java
public class Outer {
    private String outerField = "外部类字段";
    private static String staticField = "静态字段";

    // 成员内部类
    public class Inner {
        private String innerField = "内部类字段";

        public void innerMethod() {
            // 可以直接访问外部类成员
            System.out.println("访问外部类字段：" + outerField);
            System.out.println("访问静态字段：" + staticField);
            System.out.println("访问内部类字段：" + innerField);
        }

        public void accessOuter() {
            // 通过外部类名.this访问外部类对象
            System.out.println("外部类对象：" + Outer.this);
        }
    }

    public void outerMethod() {
        // 外部类访问内部类
        Inner inner = new Inner();
        System.out.println("访问内部类字段：" + inner.innerField);
        inner.innerMethod();
    }
}
```

**创建内部类对象：**
```java
public class InnerClassDemo {
    public static void main(String[] args) {
        // 方式1：通过外部类对象创建
        Outer outer = new Outer();
        Outer.Inner inner = outer.new Inner();
        inner.innerMethod();

        // 方式2：链式创建
        Outer.Inner inner2 = new Outer().new Inner();
        inner2.innerMethod();
    }
}
```

#### 7.2 静态内部类 (Static Inner Class)
**核心概念：**
- 使用static修饰的内部类
- 不依赖于外部类对象，可以直接创建
- 只能访问外部类的静态成员
- 常用于辅助类的实现

**语法格式：**
```java
public class Outer {
    private String instanceField = "实例字段";
    private static String staticField = "静态字段";

    // 静态内部类
    public static class StaticInner {
        private String innerField = "静态内部类字段";

        public void innerMethod() {
            // 只能访问外部类的静态成员
            System.out.println("访问静态字段：" + staticField);
            // System.out.println(instanceField);  // 编译错误
            System.out.println("内部类字段：" + innerField);
        }

        public static void staticInnerMethod() {
            System.out.println("静态内部类的静态方法");
        }
    }
}
```

**创建静态内部类对象：**
```java
public class StaticInnerDemo {
    public static void main(String[] args) {
        // 直接通过外部类名创建
        Outer.StaticInner staticInner = new Outer.StaticInner();
        staticInner.innerMethod();

        // 调用静态方法
        Outer.StaticInner.staticInnerMethod();
    }
}
```

#### 7.3 局部内部类 (Local Inner Class)
**核心概念：**
- 定义在方法、代码块或构造器内部的类
- 只能在定义它的方法内部使用
- 可以访问外部类成员和方法的final或effectively final变量
- 不能使用访问修饰符

**语法格式：**
```java
public class Outer {
    private String outerField = "外部类字段";

    public void outerMethod() {
        String localVar = "局部变量";
        final String finalVar = "final变量";

        // 局部内部类
        class LocalInner {
            private String innerField = "局部内部类字段";

            public void innerMethod() {
                System.out.println("外部类字段：" + outerField);
                System.out.println("局部变量：" + localVar);  // effectively final
                System.out.println("final变量：" + finalVar);
                System.out.println("内部类字段：" + innerField);
            }
        }

        // 在方法内部创建和使用
        LocalInner localInner = new LocalInner();
        localInner.innerMethod();
    }
}
```

#### 7.4 匿名内部类 (Anonymous Inner Class)
**核心概念：**
- 没有名字的内部类
- 通常用于实现接口或继承类的临时实现
- 常用于事件处理、回调函数等场景
- 是局部内部类的特殊形式

**实现接口的匿名内部类：**
```java
public interface Runnable {
    void run();
}

public class AnonymousDemo {
    public static void main(String[] args) {
        // 传统方式：创建实现类
        class MyRunnable implements Runnable {
            @Override
            public void run() {
                System.out.println("传统实现方式");
            }
        }
        Runnable r1 = new MyRunnable();

        // 匿名内部类方式
        Runnable r2 = new Runnable() {
            @Override
            public void run() {
                System.out.println("匿名内部类实现");
            }
        };

        r1.run();
        r2.run();
    }
}
```

**继承类的匿名内部类：**
```java
public abstract class Animal {
    public abstract void eat();

    public void sleep() {
        System.out.println("动物在睡觉");
    }
}

public class AnonymousExtendDemo {
    public static void main(String[] args) {
        // 匿名内部类继承抽象类
        Animal animal = new Animal() {
            @Override
            public void eat() {
                System.out.println("匿名动物在吃东西");
            }
        };

        animal.eat();
        animal.sleep();
    }
}
```

**GUI事件处理应用：**
```java
import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class GUIDemo {
    public static void main(String[] args) {
        JFrame frame = new JFrame("匿名内部类示例");
        JButton button = new JButton("点击我");

        // 使用匿名内部类处理事件
        button.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                JOptionPane.showMessageDialog(frame, "按钮被点击了！");
            }
        });

        frame.add(button);
        frame.setSize(300, 200);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}
```

### 🔧 泛型 (Generics)

#### 8.1 泛型基础概念
**核心定义：**
- 参数化类型，将类型作为参数传递
- 在编译时提供类型安全检查
- 消除类型转换，提高代码可读性
- JDK5引入的重要特性

**泛型的优势：**
```java
// 没有泛型的时代
List list = new ArrayList();
list.add("字符串");
list.add(123);  // 可以添加任意类型
String str = (String) list.get(0);  // 需要强制转换
// String str2 = (String) list.get(1);  // 运行时异常

// 使用泛型
List<String> stringList = new ArrayList<>();
stringList.add("字符串");
// stringList.add(123);  // 编译错误，类型安全
String str = stringList.get(0);  // 无需强制转换
```

#### 8.2 泛型类 (Generic Classes)
**定义泛型类：**
```java
public class Box<T> {
    private T content;

    public Box() {}

    public Box(T content) {
        this.content = content;
    }

    public T getContent() {
        return content;
    }

    public void setContent(T content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "Box{content=" + content + "}";
    }
}
```

**使用泛型类：**
```java
public class GenericClassDemo {
    public static void main(String[] args) {
        // 创建不同类型的Box
        Box<String> stringBox = new Box<>("Hello");
        Box<Integer> intBox = new Box<>(123);
        Box<Double> doubleBox = new Box<>(3.14);

        System.out.println(stringBox.getContent());  // Hello
        System.out.println(intBox.getContent());     // 123
        System.out.println(doubleBox.getContent());  // 3.14

        // 类型安全
        // stringBox.setContent(123);  // 编译错误
    }
}
```

#### 8.3 泛型方法 (Generic Methods)
**定义泛型方法：**
```java
public class GenericMethodDemo {
    // 泛型方法：在返回类型前声明类型参数
    public static <T> void swap(T[] array, int i, int j) {
        T temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    // 多个类型参数
    public static <T, U> void printPair(T first, U second) {
        System.out.println("First: " + first + ", Second: " + second);
    }

    // 有界类型参数
    public static <T extends Number> double sum(T[] numbers) {
        double total = 0.0;
        for (T num : numbers) {
            total += num.doubleValue();
        }
        return total;
    }
}
```

**使用泛型方法：**
```java
public class GenericMethodTest {
    public static void main(String[] args) {
        // 交换数组元素
        String[] strings = {"A", "B", "C"};
        GenericMethodDemo.swap(strings, 0, 2);
        System.out.println(Arrays.toString(strings));  // [C, B, A]

        Integer[] numbers = {1, 2, 3};
        GenericMethodDemo.swap(numbers, 0, 1);
        System.out.println(Arrays.toString(numbers));  // [2, 1, 3]

        // 打印不同类型的对
        GenericMethodDemo.printPair("Hello", 123);
        GenericMethodDemo.printPair(3.14, true);

        // 计算数字数组的和
        Double[] doubles = {1.1, 2.2, 3.3};
        double sum = GenericMethodDemo.sum(doubles);
        System.out.println("Sum: " + sum);  // Sum: 6.6
    }
}
```

#### 8.4 泛型接口 (Generic Interfaces)
**定义泛型接口：**
```java
public interface Comparable<T> {
    int compareTo(T other);
}

public interface Generator<T> {
    T generate();
}

// 自定义泛型接口
public interface Processor<T> {
    T process(T input);
    boolean validate(T input);
}
```

**实现泛型接口：**
```java
// 方式1：实现时指定具体类型
public class StringProcessor implements Processor<String> {
    @Override
    public String process(String input) {
        return input.toUpperCase();
    }

    @Override
    public boolean validate(String input) {
        return input != null && !input.isEmpty();
    }
}

// 方式2：实现类也使用泛型
public class GenericProcessor<T> implements Processor<T> {
    @Override
    public T process(T input) {
        System.out.println("Processing: " + input);
        return input;
    }

    @Override
    public boolean validate(T input) {
        return input != null;
    }
}
```

#### 8.5 通配符 (Wildcards)
**上界通配符 (? extends)：**
```java
public class WildcardDemo {
    // 只能读取，不能写入（除了null）
    public static double sumOfNumbers(List<? extends Number> numbers) {
        double sum = 0.0;
        for (Number num : numbers) {
            sum += num.doubleValue();
        }
        return sum;
    }

    public static void main(String[] args) {
        List<Integer> integers = Arrays.asList(1, 2, 3);
        List<Double> doubles = Arrays.asList(1.1, 2.2, 3.3);

        System.out.println(sumOfNumbers(integers));  // 6.0
        System.out.println(sumOfNumbers(doubles));   // 6.6
    }
}
```

**下界通配符 (? super)：**
```java
public class SuperWildcardDemo {
    // 只能写入，读取时只能赋值给Object
    public static void addNumbers(List<? super Integer> list) {
        list.add(1);
        list.add(2);
        list.add(3);
        // Integer num = list.get(0);  // 编译错误
        Object obj = list.get(0);     // 只能赋值给Object
    }

    public static void main(String[] args) {
        List<Number> numbers = new ArrayList<>();
        List<Object> objects = new ArrayList<>();

        addNumbers(numbers);
        addNumbers(objects);

        System.out.println(numbers);  // [1, 2, 3]
        System.out.println(objects);  // [1, 2, 3]
    }
}
```

**无界通配符 (?)：**
```java
public class UnboundedWildcardDemo {
    public static void printList(List<?> list) {
        for (Object item : list) {
            System.out.println(item);
        }
    }

    public static int getSize(List<?> list) {
        return list.size();
    }
}
```

### 🎯 枚举 (Enums)

#### 9.1 枚举基础概念
**核心定义：**
- 使用`enum`关键字定义的特殊类
- 用于定义一组固定的常量
- 每个枚举值都是该枚举类型的实例
- 提供类型安全的常量定义方式

**基础语法：**
```java
public enum Season {
    SPRING, SUMMER, AUTUMN, WINTER
}

public enum Color {
    RED, GREEN, BLUE, YELLOW, BLACK, WHITE
}
```

**枚举的使用：**
```java
public class EnumDemo {
    public static void main(String[] args) {
        // 枚举变量声明和赋值
        Season season = Season.SPRING;
        Color color = Color.RED;

        // 枚举比较
        if (season == Season.SPRING) {
            System.out.println("春天来了");
        }

        // switch语句中使用枚举
        switch (season) {
            case SPRING:
                System.out.println("春暖花开");
                break;
            case SUMMER:
                System.out.println("夏日炎炎");
                break;
            case AUTUMN:
                System.out.println("秋高气爽");
                break;
            case WINTER:
                System.out.println("冬雪纷飞");
                break;
        }
    }
}
```

#### 9.2 枚举的常用方法
**内置方法：**
```java
public class EnumMethodDemo {
    public static void main(String[] args) {
        Season season = Season.SUMMER;

        // name()：返回枚举常量的名称
        System.out.println(season.name());  // SUMMER

        // ordinal()：返回枚举常量的序号（从0开始）
        System.out.println(season.ordinal());  // 1

        // toString()：返回枚举常量的字符串表示
        System.out.println(season.toString());  // SUMMER

        // values()：返回所有枚举常量的数组
        Season[] seasons = Season.values();
        for (Season s : seasons) {
            System.out.println(s.name() + " - " + s.ordinal());
        }

        // valueOf()：根据名称获取枚举常量
        Season spring = Season.valueOf("SPRING");
        System.out.println(spring);  // SPRING
    }
}
```

#### 9.3 带参数的枚举
**枚举构造器和成员：**
```java
public enum Planet {
    MERCURY(3.303e+23, 2.4397e6),
    VENUS(4.869e+24, 6.0518e6),
    EARTH(5.976e+24, 6.37814e6),
    MARS(6.421e+23, 3.3972e6);

    private final double mass;    // 质量（千克）
    private final double radius;  // 半径（米）

    // 枚举构造器必须是private
    Planet(double mass, double radius) {
        this.mass = mass;
        this.radius = radius;
    }

    public double getMass() {
        return mass;
    }

    public double getRadius() {
        return radius;
    }

    // 计算表面重力
    public double surfaceGravity() {
        final double G = 6.67300E-11;
        return G * mass / (radius * radius);
    }

    // 计算在该星球上的重量
    public double surfaceWeight(double otherMass) {
        return otherMass * surfaceGravity();
    }
}
```

**使用带参数的枚举：**
```java
public class PlanetDemo {
    public static void main(String[] args) {
        double earthWeight = 70.0;  // 地球上的重量（千克）

        for (Planet planet : Planet.values()) {
            double weight = planet.surfaceWeight(earthWeight);
            System.out.printf("在%s上的重量: %.2f kg%n",
                            planet.name(), weight);
        }
    }
}
```

#### 9.4 枚举实现接口
**枚举实现接口：**
```java
public interface Describable {
    String getDescription();
}

public enum Status implements Describable {
    PENDING("等待处理"),
    PROCESSING("正在处理"),
    COMPLETED("已完成"),
    FAILED("处理失败");

    private final String description;

    Status(String description) {
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    // 根据描述查找枚举
    public static Status findByDescription(String desc) {
        for (Status status : values()) {
            if (status.description.equals(desc)) {
                return status;
            }
        }
        return null;
    }
}
```

### 📊 Day3核心知识点总结

#### 🎯 内部类要点
1. **成员内部类**：可访问外部类所有成员，需要外部类对象才能创建
2. **静态内部类**：独立于外部类对象，只能访问外部类静态成员
3. **局部内部类**：定义在方法内，只能访问final或effectively final变量
4. **匿名内部类**：没有名字，常用于接口实现和事件处理
5. **应用场景**：GUI事件处理、回调函数、辅助类实现

#### 🎯 泛型要点
1. **类型安全**：编译时检查类型，避免ClassCastException
2. **消除转换**：无需显式类型转换，提高代码可读性
3. **代码复用**：一套代码处理多种类型
4. **通配符**：? extends（上界）、? super（下界）、?（无界）
5. **类型擦除**：运行时泛型信息被擦除，保持向后兼容

#### 🎯 枚举要点
1. **类型安全**：编译时检查，避免使用无效常量
2. **单例保证**：每个枚举值都是单例
3. **功能丰富**：可以有构造器、方法、字段
4. **接口实现**：枚举可以实现接口
5. **应用场景**：状态机、配置选项、常量定义

#### 🎯 设计模式应用
- **单例模式**：枚举实现单例（最佳实践）
- **策略模式**：枚举实现不同策略
- **工厂模式**：泛型工厂方法
- **观察者模式**：匿名内部类实现监听器

---

## 🛠️ Day4: 常用API与工具类

### 📌 核心学习目标
- 掌握String类的常用方法和性能优化
- 熟练运用StringBuilder和StringJoiner
- 理解包装类的自动装箱和拆箱机制
- 掌握JDK8时间API的使用方法
- 了解System和Runtime类的系统操作

### 📝 字符串处理 (String Processing)

#### 10.1 String类核心特性
**不可变性 (Immutability)：**
```java
public class StringImmutableDemo {
    public static void main(String[] args) {
        String str1 = "Hello";
        String str2 = str1;

        str1 = str1 + " World";  // 创建新对象，原对象不变

        System.out.println(str1);  // Hello World
        System.out.println(str2);  // Hello（原对象未改变）

        // 字符串常量池
        String s1 = "Java";
        String s2 = "Java";
        String s3 = new String("Java");

        System.out.println(s1 == s2);        // true（指向同一对象）
        System.out.println(s1 == s3);        // false（不同对象）
        System.out.println(s1.equals(s3));   // true（内容相同）
    }
}
```

#### 10.2 String常用方法
**字符串判断方法：**
```java
public class StringMethodDemo {
    public static void main(String[] args) {
        String str = "Hello World Java";

        // 长度和判空
        System.out.println(str.length());        // 17
        System.out.println(str.isEmpty());       // false
        System.out.println("".isEmpty());        // true

        // 字符和子串判断
        System.out.println(str.charAt(6));       // W
        System.out.println(str.contains("World")); // true
        System.out.println(str.startsWith("Hello")); // true
        System.out.println(str.endsWith("Java"));    // true

        // 位置查找
        System.out.println(str.indexOf("o"));        // 4（第一次出现）
        System.out.println(str.lastIndexOf("o"));    // 7（最后一次出现）
        System.out.println(str.indexOf("Python"));   // -1（未找到）
    }
}
```

**字符串转换方法：**
```java
public class StringConvertDemo {
    public static void main(String[] args) {
        String str = "  Hello World Java  ";

        // 大小写转换
        System.out.println(str.toUpperCase());   // "  HELLO WORLD JAVA  "
        System.out.println(str.toLowerCase());   // "  hello world java  "

        // 去除空白
        System.out.println(str.trim());          // "Hello World Java"
        System.out.println(str.strip());         // "Hello World Java"（JDK11+）

        // 替换操作
        System.out.println(str.replace("World", "Java"));     // 替换所有
        System.out.println(str.replaceFirst("l", "L"));       // 替换第一个
        System.out.println(str.replaceAll("\\s+", "-"));      // 正则替换

        // 分割操作
        String[] words = str.trim().split(" ");
        System.out.println(Arrays.toString(words));  // [Hello, World, Java]

        // 截取操作
        System.out.println(str.substring(7));        // "World Java  "
        System.out.println(str.substring(7, 12));    // "World"
    }
}
```

#### 10.3 StringBuilder性能优化
**StringBuilder vs String：**
```java
public class StringBuilderDemo {
    public static void main(String[] args) {
        // 低效的字符串拼接
        long start1 = System.currentTimeMillis();
        String result1 = "";
        for (int i = 0; i < 10000; i++) {
            result1 += i;  // 每次都创建新对象
        }
        long end1 = System.currentTimeMillis();
        System.out.println("String拼接耗时：" + (end1 - start1) + "ms");

        // 高效的字符串拼接
        long start2 = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            sb.append(i);  // 在内部缓冲区操作
        }
        String result2 = sb.toString();
        long end2 = System.currentTimeMillis();
        System.out.println("StringBuilder拼接耗时：" + (end2 - start2) + "ms");
    }
}
```

**StringBuilder常用方法：**
```java
public class StringBuilderMethodDemo {
    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder("Hello");

        // 追加操作
        sb.append(" World");
        sb.append(123);
        sb.append(true);
        System.out.println(sb);  // Hello World123true

        // 插入操作
        sb.insert(5, " Java");
        System.out.println(sb);  // Hello Java World123true

        // 删除操作
        sb.delete(5, 10);        // 删除指定范围
        sb.deleteCharAt(5);      // 删除指定位置字符
        System.out.println(sb);  // HelloWorld123true

        // 替换操作
        sb.replace(0, 5, "Hi");
        System.out.println(sb);  // HiWorld123true

        // 反转操作
        sb.reverse();
        System.out.println(sb);  // eurt321dlroWiH

        // 容量管理
        System.out.println("长度：" + sb.length());
        System.out.println("容量：" + sb.capacity());
    }
}
```

#### 10.4 StringJoiner字符串连接
**StringJoiner基础用法：**
```java
import java.util.StringJoiner;

public class StringJoinerDemo {
    public static void main(String[] args) {
        // 基础用法
        StringJoiner sj1 = new StringJoiner(", ");
        sj1.add("Apple");
        sj1.add("Banana");
        sj1.add("Orange");
        System.out.println(sj1);  // Apple, Banana, Orange

        // 带前缀和后缀
        StringJoiner sj2 = new StringJoiner(", ", "[", "]");
        sj2.add("Java");
        sj2.add("Python");
        sj2.add("C++");
        System.out.println(sj2);  // [Java, Python, C++]

        // 合并StringJoiner
        StringJoiner sj3 = new StringJoiner(" | ");
        sj3.add("First");
        sj3.add("Second");

        StringJoiner sj4 = new StringJoiner(" | ");
        sj4.add("Third");
        sj4.add("Fourth");

        sj3.merge(sj4);
        System.out.println(sj3);  // First | Second | Third | Fourth
    }
}
```

### 📦 包装类 (Wrapper Classes)

#### 11.1 基本类型与包装类对应
**对应关系：**
```java
// 基本类型    包装类
byte      -> Byte
short     -> Short
int       -> Integer
long      -> Long
float     -> Float
double    -> Double
char      -> Character
boolean   -> Boolean
```

#### 11.2 自动装箱和拆箱
**装箱拆箱机制：**
```java
public class AutoBoxingDemo {
    public static void main(String[] args) {
        // 自动装箱：基本类型 → 包装类
        Integer i1 = 100;           // 等价于 Integer.valueOf(100)
        Double d1 = 3.14;           // 等价于 Double.valueOf(3.14)
        Boolean b1 = true;          // 等价于 Boolean.valueOf(true)

        // 自动拆箱：包装类 → 基本类型
        int i2 = i1;                // 等价于 i1.intValue()
        double d2 = d1;             // 等价于 d1.doubleValue()
        boolean b2 = b1;            // 等价于 b1.booleanValue()

        // 混合运算
        Integer a = 10;
        Integer b = 20;
        Integer c = a + b;          // 自动拆箱运算后装箱
        System.out.println(c);      // 30
    }
}
```

**缓存机制：**
```java
public class WrapperCacheDemo {
    public static void main(String[] args) {
        // Integer缓存 -128 到 127
        Integer i1 = 127;
        Integer i2 = 127;
        Integer i3 = 128;
        Integer i4 = 128;

        System.out.println(i1 == i2);  // true（缓存范围内）
        System.out.println(i3 == i4);  // false（超出缓存范围）

        // 其他包装类的缓存
        Boolean b1 = true;
        Boolean b2 = true;
        System.out.println(b1 == b2);  // true（Boolean缓存true/false）

        Character c1 = 'A';
        Character c2 = 'A';
        System.out.println(c1 == c2);  // true（Character缓存0-127）
    }
}
```

---
