# Java学习总结 - Day1到Day11完整手册

## 📚 学习概览

本文档系统总结了Java SE核心技术的完整学习路径，从面向对象编程基础到高级特性应用，涵盖了Java开发的核心知识体系。

### 🎯 学习目标
- 掌握Java面向对象编程的核心思想和实现机制
- 熟练运用Java常用API和集合框架
- 理解异常处理和IO流操作
- 具备Java项目开发的基础能力

### 📖 知识体系结构
```
Java SE核心技术
├── 面向对象编程基础 (Day1-Day3)
│   ├── Static静态特性与继承机制
│   ├── 多态、抽象类与接口编程
│   └── 内部类、泛型与枚举
├── 常用API与工具类 (Day4-Day5)
│   ├── 字符串处理与时间API
│   ├── Lambda表达式与函数式编程
│   └── 正则表达式与方法引用
├── 集合框架与异常处理 (Day6-Day7)
│   ├── 异常处理机制
│   ├── Collection集合体系
│   └── Map集合与数据结构
├── 流处理与文件操作 (Day8-Day11)
│   ├── Stream流式编程
│   ├── IO流体系
│   ├── 文件操作与NIO
│   └── 特殊文件处理与日志
```

---

## 🔧 Day1: Static静态特性与继承机制

### 📌 核心学习目标
- 理解static关键字的作用机制和应用场景
- 掌握Java继承的实现原理和使用规范
- 熟练运用访问修饰符控制成员访问权限
- 理解方法重写和构造器调用规则

### 🎯 Static静态特性

#### 1.1 静态变量 (Static Variables)
**核心概念：**
- 使用`static`关键字修饰的成员变量
- 在内存中只有一份，被类和所有对象共享
- 随类加载而加载，优先于对象创建

**语法格式：**
```java
public class Student {
    static String schoolName = "黑马程序员";  // 静态变量
    int age;  // 实例变量
}
```

**访问方式：**
```java
// 推荐方式：类名.静态变量
Student.schoolName = "新学校名";

// 不推荐：对象名.静态变量
Student s = new Student();
s.schoolName = "新学校名";  // 不推荐
```

**关键特点：**
- 内存共享：所有对象共享同一份静态变量
- 类级别：属于类，不属于任何具体对象
- 生命周期：随类加载创建，随类卸载销毁

#### 1.2 静态方法 (Static Methods)
**核心概念：**
- 使用`static`关键字修饰的方法
- 可以直接通过类名调用，无需创建对象
- 常用于工具类和通用功能实现

**语法格式：**
```java
public class MathUtil {
    public static int add(int a, int b) {
        return a + b;
    }
}

// 调用方式
int result = MathUtil.add(10, 20);
```

**使用限制：**
```java
public class Demo {
    static String staticVar = "静态变量";
    String instanceVar = "实例变量";
    
    public static void staticMethod() {
        System.out.println(staticVar);      // ✓ 可以访问静态成员
        // System.out.println(instanceVar); // ✗ 不能直接访问实例成员
        // this.instanceVar;                // ✗ 不能使用this关键字
    }
    
    public void instanceMethod() {
        System.out.println(staticVar);      // ✓ 可以访问静态成员
        System.out.println(instanceVar);    // ✓ 可以访问实例成员
        System.out.println(this.instanceVar); // ✓ 可以使用this关键字
    }
}
```

#### 1.3 静态代码块 (Static Code Blocks)
**核心概念：**
- 使用`static {}`语法定义
- 在类加载时自动执行，且只执行一次
- 用于初始化静态资源和执行类级别的初始化操作

**语法格式：**
```java
public class InitDemo {
    public static String config;
    public static List<String> dataList = new ArrayList<>();
    
    // 静态代码块
    static {
        System.out.println("静态代码块执行");
        config = "默认配置";
        dataList.add("初始数据1");
        dataList.add("初始数据2");
    }
    
    public static void main(String[] args) {
        System.out.println("main方法执行");
        System.out.println("配置：" + config);
        System.out.println("数据：" + dataList);
    }
}
```

**执行顺序：**
```
静态代码块 → main方法 → 实例代码块 → 构造器
```

#### 1.4 单例设计模式 (Singleton Pattern)
**设计目的：**
- 确保一个类只有一个实例
- 提供全局访问点
- 节省系统资源

**饿汉式单例（推荐）：**
```java
public class Singleton {
    // 私有静态变量，类加载时创建唯一实例
    private static final Singleton INSTANCE = new Singleton();
    
    // 私有构造器，防止外部实例化
    private Singleton() {}
    
    // 公共静态方法，提供全局访问点
    public static Singleton getInstance() {
        return INSTANCE;
    }
}
```

**懒汉式单例：**
```java
public class LazySingleton {
    private static LazySingleton instance;
    
    private LazySingleton() {}
    
    public static LazySingleton getInstance() {
        if (instance == null) {
            instance = new LazySingleton();
        }
        return instance;
    }
}
```

**对比分析：**
| 特性 | 饿汉式 | 懒汉式 |
|------|--------|--------|
| 创建时机 | 类加载时 | 第一次使用时 |
| 线程安全 | 天然线程安全 | 需要同步处理 |
| 内存占用 | 可能浪费内存 | 节省内存 |
| 性能 | 无同步开销 | 有同步开销 |

### 🏗️ 继承机制 (Inheritance)

#### 2.1 基础继承概念
**核心概念：**
- 使用`extends`关键字实现类的继承关系
- 子类自动获得父类的非私有成员
- 实现代码复用和建立类的层次结构

**语法格式：**
```java
// 父类
public class Animal {
    protected String name;
    private int age;  // 私有成员，子类不能直接访问
    
    public Animal(String name) {
        this.name = name;
    }
    
    public void eat() {
        System.out.println(name + "在吃东西");
    }
    
    protected void sleep() {
        System.out.println(name + "在睡觉");
    }
}

// 子类
public class Dog extends Animal {
    private String breed;
    
    public Dog(String name, String breed) {
        super(name);  // 调用父类构造器
        this.breed = breed;
    }
    
    public void bark() {
        System.out.println(name + "在汪汪叫");  // 可以访问protected成员
    }
    
    @Override
    public void eat() {
        System.out.println(name + "正在吃狗粮");  // 方法重写
    }
}
```

#### 2.2 访问修饰符权限控制
**权限级别（从小到大）：**
```
private < 默认(package) < protected < public
```

**权限控制表：**
| 修饰符 | 本类 | 同包 | 子类 | 任意位置 |
|--------|------|------|------|----------|
| private | ✓ | ✗ | ✗ | ✗ |
| 默认 | ✓ | ✓ | ✗ | ✗ |
| protected | ✓ | ✓ | ✓ | ✗ |
| public | ✓ | ✓ | ✓ | ✓ |

**实际应用示例：**
```java
public class AccessDemo {
    private String privateField = "私有字段";
    String packageField = "包访问字段";
    protected String protectedField = "受保护字段";
    public String publicField = "公共字段";
    
    private void privateMethod() { }
    void packageMethod() { }
    protected void protectedMethod() { }
    public void publicMethod() { }
}
```

#### 2.3 方法重写 (Method Overriding)
**核心概念：**
- 子类重新定义父类的方法实现
- 使用`@Override`注解确保重写正确性
- 为多态机制提供技术基础

**重写规则：**
```java
public class Vehicle {
    public void start() {
        System.out.println("车辆启动");
    }
    
    protected void stop() {
        System.out.println("车辆停止");
    }
}

public class Car extends Vehicle {
    @Override
    public void start() {  // 访问权限不能比父类更严格
        System.out.println("汽车点火启动");
    }
    
    @Override
    public void stop() {  // 可以扩大访问权限
        System.out.println("汽车刹车停止");
    }
}
```

**重写约束：**
1. 方法名、参数列表、返回类型必须相同
2. 访问权限不能比父类更严格
3. 不能重写private、static、final方法
4. 抛出的异常不能比父类更宽泛

#### 2.4 构造器调用机制
**核心原理：**
- 子类构造器必须先调用父类构造器
- 默认调用父类无参构造器
- 可以使用`super()`显式调用父类构造器

**调用示例：**
```java
public class Person {
    private String name;
    private int age;
    
    public Person() {
        System.out.println("Person无参构造器");
    }
    
    public Person(String name, int age) {
        this.name = name;
        this.age = age;
        System.out.println("Person有参构造器");
    }
}

public class Student extends Person {
    private String school;
    
    public Student() {
        super();  // 显式调用父类无参构造器（可省略）
        System.out.println("Student无参构造器");
    }
    
    public Student(String name, int age, String school) {
        super(name, age);  // 调用父类有参构造器
        this.school = school;
        System.out.println("Student有参构造器");
    }
}
```

**执行顺序：**
```
父类构造器 → 子类构造器
```

#### 2.5 this和super关键字
**this关键字用法：**
```java
public class ThisDemo {
    private String name;
    
    public ThisDemo(String name) {
        this.name = name;  // 区分参数和成员变量
    }
    
    public ThisDemo() {
        this("默认名称");  // 调用本类其他构造器
    }
    
    public void setName(String name) {
        this.name = name;  // 访问当前对象的成员变量
    }
    
    public void printInfo() {
        this.setName("新名称");  // 调用当前对象的方法
    }
}
```

**super关键字用法：**
```java
public class SuperDemo extends ThisDemo {
    private String title;
    
    public SuperDemo(String name, String title) {
        super(name);  // 调用父类构造器
        this.title = title;
    }
    
    @Override
    public void printInfo() {
        super.printInfo();  // 调用父类方法
        System.out.println("标题：" + title);
    }
}
```

### 📊 Day1核心知识点总结

#### 🎯 Static特性要点
1. **内存特点**：静态成员在内存中只有一份，类级别共享
2. **加载时机**：随类加载而加载，优先于对象创建
3. **访问方式**：推荐使用类名直接访问
4. **应用场景**：工具类、单例模式、共享数据
5. **使用限制**：静态方法只能访问静态成员，不能使用this

#### 🎯 继承机制要点
1. **代码复用**：子类自动获得父类的非私有成员
2. **层次结构**：建立类之间的is-a关系
3. **方法重写**：子类可以重新定义父类方法的行为
4. **构造顺序**：先调用父类构造器，再调用子类构造器
5. **访问控制**：通过修饰符控制继承的范围
6. **成员访问**：遵循就近原则（局部→子类→父类）

#### 🎯 关键字使用总结
- **static**：修饰类级别的成员，实现共享和工具功能
- **extends**：实现类的继承关系
- **@Override**：确保方法重写的正确性
- **super**：访问父类成员和构造器
- **this**：访问当前类成员和构造器

---

## 🚀 Day2: 多态、Final关键字、抽象类与接口编程

### 📌 核心学习目标
- 深入理解多态的实现机制和应用场景
- 掌握final关键字的多种用法和约束规则
- 熟练运用抽象类设计模板方法模式
- 精通接口编程思想和JDK8新特性
- 理解面向对象设计的核心原则

### 🎭 多态机制 (Polymorphism)

#### 3.1 多态基础概念
**核心定义：**
- 同一个引用类型，使用不同的实例而执行不同操作
- 编译看左边，运行看右边
- 实现代码的灵活性和可扩展性

**多态的形式：**
```java
// 父类引用指向子类对象
Animal animal = new Dog();  // 向上转型
animal.eat();  // 调用Dog类重写的eat方法

// 接口引用指向实现类对象
List<String> list = new ArrayList<>();  // 多态应用
```

**多态成立的条件：**
1. 有继承/实现关系
2. 有方法重写
3. 有父类引用指向子类对象

#### 3.2 多态的内存机制
**动态绑定原理：**
```java
public class Animal {
    public void eat() {
        System.out.println("动物在吃东西");
    }

    public void sleep() {
        System.out.println("动物在睡觉");
    }
}

public class Cat extends Animal {
    @Override
    public void eat() {
        System.out.println("猫在吃鱼");
    }

    // 子类特有方法
    public void catchMouse() {
        System.out.println("猫在抓老鼠");
    }
}

public class Dog extends Animal {
    @Override
    public void eat() {
        System.out.println("狗在吃骨头");
    }

    // 子类特有方法
    public void watchHouse() {
        System.out.println("狗在看家");
    }
}
```

**多态调用示例：**
```java
public class PolymorphismDemo {
    public static void main(String[] args) {
        // 多态数组
        Animal[] animals = {
            new Cat(),
            new Dog(),
            new Cat()
        };

        // 统一调用，不同行为
        for (Animal animal : animals) {
            animal.eat();  // 运行时动态绑定到具体子类方法
            animal.sleep();
        }
    }
}
```

**输出结果：**
```
猫在吃鱼
动物在睡觉
狗在吃骨头
动物在睡觉
猫在吃鱼
动物在睡觉
```

#### 3.3 类型转换机制
**向上转型（自动转换）：**
```java
Animal animal = new Cat();  // 自动向上转型
// 优点：代码通用性强
// 缺点：不能调用子类特有方法
```

**向下转型（强制转换）：**
```java
Animal animal = new Cat();

// 强制向下转型
Cat cat = (Cat) animal;
cat.catchMouse();  // 现在可以调用子类特有方法

// 错误的向下转型会导致ClassCastException
// Dog dog = (Dog) animal;  // 运行时异常
```

**instanceof运算符：**
```java
public static void handleAnimal(Animal animal) {
    // 安全的类型判断和转换
    if (animal instanceof Cat) {
        Cat cat = (Cat) animal;
        cat.catchMouse();
    } else if (animal instanceof Dog) {
        Dog dog = (Dog) animal;
        dog.watchHouse();
    }

    // 通用操作
    animal.eat();
}
```

#### 3.4 多态的优势和应用
**代码示例：动物管理系统**
```java
public class AnimalManager {
    // 多态参数：可以接收任何Animal子类对象
    public void feedAnimal(Animal animal) {
        animal.eat();  // 不同动物有不同的吃法
    }

    // 多态数组：统一管理不同类型的动物
    public void feedAllAnimals(Animal[] animals) {
        for (Animal animal : animals) {
            feedAnimal(animal);
        }
    }

    // 多态返回值：根据条件返回不同的子类对象
    public Animal createAnimal(String type) {
        switch (type) {
            case "cat": return new Cat();
            case "dog": return new Dog();
            default: return new Animal();
        }
    }
}
```

**多态的优势：**
1. **提高代码复用性**：一套代码处理多种类型
2. **增强可扩展性**：新增子类无需修改现有代码
3. **降低耦合度**：依赖抽象而非具体实现
4. **提升维护性**：修改子类行为不影响调用方

### 🔒 Final关键字

#### 4.1 Final修饰类
**核心概念：**
- final修饰的类不能被继承
- 常见的final类：String、Integer、LocalDate等

**语法示例：**
```java
// final类不能被继承
public final class FinalClass {
    public void method() {
        System.out.println("final类的方法");
    }
}

// 编译错误：Cannot inherit from final class
// public class SubClass extends FinalClass { }
```

**应用场景：**
- 工具类：如Math、Arrays
- 不可变类：如String、包装类
- 安全敏感类：防止恶意继承

#### 4.2 Final修饰方法
**核心概念：**
- final修饰的方法不能被子类重写
- 保证方法行为的一致性

**语法示例：**
```java
public class Parent {
    // final方法不能被重写
    public final void finalMethod() {
        System.out.println("这是final方法");
    }

    public void normalMethod() {
        System.out.println("普通方法");
    }
}

public class Child extends Parent {
    // 编译错误：Cannot override the final method
    // public void finalMethod() { }

    @Override
    public void normalMethod() {
        System.out.println("重写的普通方法");
    }
}
```

#### 4.3 Final修饰变量
**修饰局部变量：**
```java
public void method() {
    final int x = 10;
    // x = 20;  // 编译错误：Cannot assign a value to final variable

    final List<String> list = new ArrayList<>();
    list.add("元素");  // 可以修改对象内容
    // list = new ArrayList<>();  // 编译错误：不能重新赋值
}
```

**修饰成员变量：**
```java
public class FinalFieldDemo {
    // 方式1：声明时初始化
    private final String name = "默认名称";

    // 方式2：构造器中初始化
    private final int age;

    // 方式3：实例代码块中初始化
    private final String address;
    {
        address = "默认地址";
    }

    public FinalFieldDemo(int age) {
        this.age = age;  // 必须在构造器中初始化
    }
}
```

**修饰静态变量（常量）：**
```java
public class Constants {
    // 公共常量：通常使用public static final
    public static final String COMPANY_NAME = "黑马程序员";
    public static final int MAX_SIZE = 100;
    public static final double PI = 3.14159;

    // 私有常量
    private static final String SECRET_KEY = "abc123";
}
```

### 🎨 抽象类 (Abstract Classes)

#### 5.1 抽象类基础概念
**核心定义：**
- 使用`abstract`关键字修饰的类
- 不能被实例化，只能被继承
- 可以包含抽象方法和具体方法
- 为子类提供通用的模板和规范

**语法格式：**
```java
public abstract class Shape {
    protected String color;

    // 构造器
    public Shape(String color) {
        this.color = color;
    }

    // 具体方法
    public void setColor(String color) {
        this.color = color;
    }

    // 抽象方法：子类必须实现
    public abstract double calculateArea();
    public abstract double calculatePerimeter();

    // 模板方法：定义算法骨架
    public final void printInfo() {
        System.out.println("颜色：" + color);
        System.out.println("面积：" + calculateArea());
        System.out.println("周长：" + calculatePerimeter());
    }
}
```

#### 5.2 抽象方法
**核心特点：**
- 使用`abstract`关键字修饰
- 只有方法声明，没有方法体
- 子类必须重写所有抽象方法

**实现示例：**
```java
// 圆形类
public class Circle extends Shape {
    private double radius;

    public Circle(String color, double radius) {
        super(color);
        this.radius = radius;
    }

    @Override
    public double calculateArea() {
        return Math.PI * radius * radius;
    }

    @Override
    public double calculatePerimeter() {
        return 2 * Math.PI * radius;
    }
}

// 矩形类
public class Rectangle extends Shape {
    private double width;
    private double height;

    public Rectangle(String color, double width, double height) {
        super(color);
        this.width = width;
        this.height = height;
    }

    @Override
    public double calculateArea() {
        return width * height;
    }

    @Override
    public double calculatePerimeter() {
        return 2 * (width + height);
    }
}
```

#### 5.3 模板方法模式
**设计思想：**
- 在抽象类中定义算法的骨架
- 将一些步骤延迟到子类中实现
- 实现代码复用和行为规范

**实际应用示例：**
```java
public abstract class DataProcessor {
    // 模板方法：定义数据处理流程
    public final void processData() {
        loadData();
        validateData();
        transformData();
        saveData();
        cleanup();
    }

    // 具体方法：通用逻辑
    private void loadData() {
        System.out.println("加载数据...");
    }

    private void cleanup() {
        System.out.println("清理资源...");
    }

    // 抽象方法：子类实现具体逻辑
    protected abstract void validateData();
    protected abstract void transformData();
    protected abstract void saveData();
}

// 具体实现类
public class XMLDataProcessor extends DataProcessor {
    @Override
    protected void validateData() {
        System.out.println("验证XML数据格式");
    }

    @Override
    protected void transformData() {
        System.out.println("转换XML数据");
    }

    @Override
    protected void saveData() {
        System.out.println("保存到XML文件");
    }
}
```

### 🔌 接口编程 (Interface Programming)

#### 6.1 接口基础概念
**核心定义：**
- 使用`interface`关键字定义
- 是一种引用数据类型，类似于类
- 定义了一组抽象方法的集合
- 实现类必须实现接口中的所有抽象方法

**JDK8之前的接口特点：**
```java
public interface Animal {
    // 常量：public static final（可省略）
    String KINGDOM = "动物界";
    int MAX_AGE = 200;

    // 抽象方法：public abstract（可省略）
    void eat();
    void sleep();
    void move();
}
```

#### 6.2 接口的实现
**单接口实现：**
```java
public class Dog implements Animal {
    @Override
    public void eat() {
        System.out.println("狗在吃骨头");
    }

    @Override
    public void sleep() {
        System.out.println("狗在睡觉");
    }

    @Override
    public void move() {
        System.out.println("狗在跑步");
    }
}
```

**多接口实现：**
```java
public interface Flyable {
    void fly();
}

public interface Swimmable {
    void swim();
}

// 一个类可以实现多个接口
public class Duck implements Animal, Flyable, Swimmable {
    @Override
    public void eat() {
        System.out.println("鸭子在吃虫子");
    }

    @Override
    public void sleep() {
        System.out.println("鸭子在睡觉");
    }

    @Override
    public void move() {
        System.out.println("鸭子在走路");
    }

    @Override
    public void fly() {
        System.out.println("鸭子在飞翔");
    }

    @Override
    public void swim() {
        System.out.println("鸭子在游泳");
    }
}
```

#### 6.3 接口的继承
**接口间的继承：**
```java
public interface Vehicle {
    void start();
    void stop();
}

public interface Car extends Vehicle {
    void drive();
    void park();
}

public interface ElectricCar extends Car {
    void charge();
    int getBatteryLevel();
}

// 实现类需要实现所有继承链上的方法
public class Tesla implements ElectricCar {
    private int batteryLevel = 100;

    @Override
    public void start() {
        System.out.println("特斯拉启动");
    }

    @Override
    public void stop() {
        System.out.println("特斯拉停止");
    }

    @Override
    public void drive() {
        System.out.println("特斯拉行驶");
    }

    @Override
    public void park() {
        System.out.println("特斯拉停车");
    }

    @Override
    public void charge() {
        System.out.println("特斯拉充电");
        batteryLevel = 100;
    }

    @Override
    public int getBatteryLevel() {
        return batteryLevel;
    }
}
```

#### 6.4 JDK8接口新特性
**默认方法 (Default Methods)：**
```java
public interface Drawable {
    // 抽象方法
    void draw();

    // 默认方法：有方法体，实现类可以选择重写
    default void setColor(String color) {
        System.out.println("设置颜色为：" + color);
    }

    default void reset() {
        System.out.println("重置绘图状态");
    }
}

public class Circle implements Drawable {
    @Override
    public void draw() {
        System.out.println("绘制圆形");
    }

    // 可以选择重写默认方法
    @Override
    public void setColor(String color) {
        System.out.println("圆形颜色设置为：" + color);
    }

    // 也可以不重写，直接使用接口的默认实现
}
```

**静态方法 (Static Methods)：**
```java
public interface MathUtils {
    // 静态方法：属于接口，通过接口名调用
    static int add(int a, int b) {
        return a + b;
    }

    static double calculateCircleArea(double radius) {
        return Math.PI * radius * radius;
    }

    // 私有静态方法（JDK9+）：为其他静态方法提供辅助
    private static void log(String message) {
        System.out.println("Log: " + message);
    }
}

// 调用方式
int result = MathUtils.add(10, 20);
double area = MathUtils.calculateCircleArea(5.0);
```

**接口中的私有方法（JDK9+）：**
```java
public interface Logger {
    default void logInfo(String message) {
        log("INFO", message);
    }

    default void logError(String message) {
        log("ERROR", message);
    }

    // 私有方法：为默认方法提供公共逻辑
    private void log(String level, String message) {
        System.out.println("[" + level + "] " + message);
    }
}
```

### 📊 抽象类 vs 接口对比

| 特性 | 抽象类 | 接口 |
|------|--------|------|
| 关键字 | abstract class | interface |
| 继承/实现 | extends（单继承） | implements（多实现） |
| 构造器 | 可以有 | 不能有 |
| 成员变量 | 任意类型 | public static final |
| 方法类型 | 抽象+具体+静态 | 抽象+默认+静态+私有 |
| 访问修饰符 | 任意 | public（默认） |
| 设计目的 | is-a关系，代码复用 | can-do能力，规范定义 |

**选择原则：**
- **使用抽象类**：当多个类有共同的属性和方法实现时
- **使用接口**：当需要定义一组规范或能力时
- **组合使用**：抽象类实现接口，提供部分默认实现

### 📊 Day2核心知识点总结

#### 🎯 多态机制要点
1. **实现条件**：继承关系 + 方法重写 + 父类引用指向子类对象
2. **动态绑定**：编译时看引用类型，运行时看实际对象类型
3. **类型转换**：向上转型自动，向下转型需要强制转换和类型检查
4. **应用优势**：提高代码复用性、可扩展性和维护性
5. **注意事项**：多态调用不能访问子类特有成员

#### 🎯 Final关键字要点
1. **修饰类**：类不能被继承（如String、Integer）
2. **修饰方法**：方法不能被重写
3. **修饰变量**：变量不能被重新赋值（常量）
4. **应用场景**：工具类、不可变类、安全敏感类
5. **注意事项**：final修饰引用类型时，引用不可变但对象内容可变

#### 🎯 抽象类要点
1. **设计目的**：为子类提供通用模板和规范
2. **核心特点**：不能实例化，可包含抽象和具体方法
3. **模板模式**：定义算法骨架，延迟部分实现到子类
4. **使用场景**：多个类有共同属性和部分共同行为
5. **继承规则**：子类必须实现所有抽象方法

#### 🎯 接口编程要点
1. **设计理念**：定义规范和能力，实现多重继承效果
2. **JDK8新特性**：默认方法、静态方法
3. **多接口实现**：一个类可以实现多个接口
4. **接口继承**：接口可以继承多个接口
5. **应用优势**：降低耦合度，提高代码灵活性

#### 🎯 面向对象设计原则
- **开闭原则**：对扩展开放，对修改关闭
- **里氏替换原则**：子类可以替换父类
- **接口隔离原则**：接口应该小而专一
- **依赖倒置原则**：依赖抽象而非具体实现

---

## 🏗️ Day3: 内部类、泛型与枚举

### 📌 核心学习目标
- 掌握四种内部类的特点和应用场景
- 理解泛型的设计思想和使用方法
- 熟练运用枚举类型解决常量定义问题
- 理解类型安全和代码复用的重要性

### 🏠 内部类 (Inner Classes)

#### 7.1 成员内部类 (Member Inner Class)
**核心概念：**
- 定义在外部类成员位置的类
- 可以访问外部类的所有成员（包括私有）
- 外部类也可以访问内部类的私有成员
- 内部类对象依赖于外部类对象存在

**语法格式：**
```java
public class Outer {
    private String outerField = "外部类字段";
    private static String staticField = "静态字段";

    // 成员内部类
    public class Inner {
        private String innerField = "内部类字段";

        public void innerMethod() {
            // 可以直接访问外部类成员
            System.out.println("访问外部类字段：" + outerField);
            System.out.println("访问静态字段：" + staticField);
            System.out.println("访问内部类字段：" + innerField);
        }

        public void accessOuter() {
            // 通过外部类名.this访问外部类对象
            System.out.println("外部类对象：" + Outer.this);
        }
    }

    public void outerMethod() {
        // 外部类访问内部类
        Inner inner = new Inner();
        System.out.println("访问内部类字段：" + inner.innerField);
        inner.innerMethod();
    }
}
```

**创建内部类对象：**
```java
public class InnerClassDemo {
    public static void main(String[] args) {
        // 方式1：通过外部类对象创建
        Outer outer = new Outer();
        Outer.Inner inner = outer.new Inner();
        inner.innerMethod();

        // 方式2：链式创建
        Outer.Inner inner2 = new Outer().new Inner();
        inner2.innerMethod();
    }
}
```

#### 7.2 静态内部类 (Static Inner Class)
**核心概念：**
- 使用static修饰的内部类
- 不依赖于外部类对象，可以直接创建
- 只能访问外部类的静态成员
- 常用于辅助类的实现

**语法格式：**
```java
public class Outer {
    private String instanceField = "实例字段";
    private static String staticField = "静态字段";

    // 静态内部类
    public static class StaticInner {
        private String innerField = "静态内部类字段";

        public void innerMethod() {
            // 只能访问外部类的静态成员
            System.out.println("访问静态字段：" + staticField);
            // System.out.println(instanceField);  // 编译错误
            System.out.println("内部类字段：" + innerField);
        }

        public static void staticInnerMethod() {
            System.out.println("静态内部类的静态方法");
        }
    }
}
```

**创建静态内部类对象：**
```java
public class StaticInnerDemo {
    public static void main(String[] args) {
        // 直接通过外部类名创建
        Outer.StaticInner staticInner = new Outer.StaticInner();
        staticInner.innerMethod();

        // 调用静态方法
        Outer.StaticInner.staticInnerMethod();
    }
}
```

#### 7.3 局部内部类 (Local Inner Class)
**核心概念：**
- 定义在方法、代码块或构造器内部的类
- 只能在定义它的方法内部使用
- 可以访问外部类成员和方法的final或effectively final变量
- 不能使用访问修饰符

**语法格式：**
```java
public class Outer {
    private String outerField = "外部类字段";

    public void outerMethod() {
        String localVar = "局部变量";
        final String finalVar = "final变量";

        // 局部内部类
        class LocalInner {
            private String innerField = "局部内部类字段";

            public void innerMethod() {
                System.out.println("外部类字段：" + outerField);
                System.out.println("局部变量：" + localVar);  // effectively final
                System.out.println("final变量：" + finalVar);
                System.out.println("内部类字段：" + innerField);
            }
        }

        // 在方法内部创建和使用
        LocalInner localInner = new LocalInner();
        localInner.innerMethod();
    }
}
```

#### 7.4 匿名内部类 (Anonymous Inner Class)
**核心概念：**
- 没有名字的内部类
- 通常用于实现接口或继承类的临时实现
- 常用于事件处理、回调函数等场景
- 是局部内部类的特殊形式

**实现接口的匿名内部类：**
```java
public interface Runnable {
    void run();
}

public class AnonymousDemo {
    public static void main(String[] args) {
        // 传统方式：创建实现类
        class MyRunnable implements Runnable {
            @Override
            public void run() {
                System.out.println("传统实现方式");
            }
        }
        Runnable r1 = new MyRunnable();

        // 匿名内部类方式
        Runnable r2 = new Runnable() {
            @Override
            public void run() {
                System.out.println("匿名内部类实现");
            }
        };

        r1.run();
        r2.run();
    }
}
```

**继承类的匿名内部类：**
```java
public abstract class Animal {
    public abstract void eat();

    public void sleep() {
        System.out.println("动物在睡觉");
    }
}

public class AnonymousExtendDemo {
    public static void main(String[] args) {
        // 匿名内部类继承抽象类
        Animal animal = new Animal() {
            @Override
            public void eat() {
                System.out.println("匿名动物在吃东西");
            }
        };

        animal.eat();
        animal.sleep();
    }
}
```

**GUI事件处理应用：**
```java
import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class GUIDemo {
    public static void main(String[] args) {
        JFrame frame = new JFrame("匿名内部类示例");
        JButton button = new JButton("点击我");

        // 使用匿名内部类处理事件
        button.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                JOptionPane.showMessageDialog(frame, "按钮被点击了！");
            }
        });

        frame.add(button);
        frame.setSize(300, 200);
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setVisible(true);
    }
}
```

### 🔧 泛型 (Generics)

#### 8.1 泛型基础概念
**核心定义：**
- 参数化类型，将类型作为参数传递
- 在编译时提供类型安全检查
- 消除类型转换，提高代码可读性
- JDK5引入的重要特性

**泛型的优势：**
```java
// 没有泛型的时代
List list = new ArrayList();
list.add("字符串");
list.add(123);  // 可以添加任意类型
String str = (String) list.get(0);  // 需要强制转换
// String str2 = (String) list.get(1);  // 运行时异常

// 使用泛型
List<String> stringList = new ArrayList<>();
stringList.add("字符串");
// stringList.add(123);  // 编译错误，类型安全
String str = stringList.get(0);  // 无需强制转换
```

#### 8.2 泛型类 (Generic Classes)
**定义泛型类：**
```java
public class Box<T> {
    private T content;

    public Box() {}

    public Box(T content) {
        this.content = content;
    }

    public T getContent() {
        return content;
    }

    public void setContent(T content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "Box{content=" + content + "}";
    }
}
```

**使用泛型类：**
```java
public class GenericClassDemo {
    public static void main(String[] args) {
        // 创建不同类型的Box
        Box<String> stringBox = new Box<>("Hello");
        Box<Integer> intBox = new Box<>(123);
        Box<Double> doubleBox = new Box<>(3.14);

        System.out.println(stringBox.getContent());  // Hello
        System.out.println(intBox.getContent());     // 123
        System.out.println(doubleBox.getContent());  // 3.14

        // 类型安全
        // stringBox.setContent(123);  // 编译错误
    }
}
```

#### 8.3 泛型方法 (Generic Methods)
**定义泛型方法：**
```java
public class GenericMethodDemo {
    // 泛型方法：在返回类型前声明类型参数
    public static <T> void swap(T[] array, int i, int j) {
        T temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    // 多个类型参数
    public static <T, U> void printPair(T first, U second) {
        System.out.println("First: " + first + ", Second: " + second);
    }

    // 有界类型参数
    public static <T extends Number> double sum(T[] numbers) {
        double total = 0.0;
        for (T num : numbers) {
            total += num.doubleValue();
        }
        return total;
    }
}
```

**使用泛型方法：**
```java
public class GenericMethodTest {
    public static void main(String[] args) {
        // 交换数组元素
        String[] strings = {"A", "B", "C"};
        GenericMethodDemo.swap(strings, 0, 2);
        System.out.println(Arrays.toString(strings));  // [C, B, A]

        Integer[] numbers = {1, 2, 3};
        GenericMethodDemo.swap(numbers, 0, 1);
        System.out.println(Arrays.toString(numbers));  // [2, 1, 3]

        // 打印不同类型的对
        GenericMethodDemo.printPair("Hello", 123);
        GenericMethodDemo.printPair(3.14, true);

        // 计算数字数组的和
        Double[] doubles = {1.1, 2.2, 3.3};
        double sum = GenericMethodDemo.sum(doubles);
        System.out.println("Sum: " + sum);  // Sum: 6.6
    }
}
```

#### 8.4 泛型接口 (Generic Interfaces)
**定义泛型接口：**
```java
public interface Comparable<T> {
    int compareTo(T other);
}

public interface Generator<T> {
    T generate();
}

// 自定义泛型接口
public interface Processor<T> {
    T process(T input);
    boolean validate(T input);
}
```

**实现泛型接口：**
```java
// 方式1：实现时指定具体类型
public class StringProcessor implements Processor<String> {
    @Override
    public String process(String input) {
        return input.toUpperCase();
    }

    @Override
    public boolean validate(String input) {
        return input != null && !input.isEmpty();
    }
}

// 方式2：实现类也使用泛型
public class GenericProcessor<T> implements Processor<T> {
    @Override
    public T process(T input) {
        System.out.println("Processing: " + input);
        return input;
    }

    @Override
    public boolean validate(T input) {
        return input != null;
    }
}
```

#### 8.5 通配符 (Wildcards)
**上界通配符 (? extends)：**
```java
public class WildcardDemo {
    // 只能读取，不能写入（除了null）
    public static double sumOfNumbers(List<? extends Number> numbers) {
        double sum = 0.0;
        for (Number num : numbers) {
            sum += num.doubleValue();
        }
        return sum;
    }

    public static void main(String[] args) {
        List<Integer> integers = Arrays.asList(1, 2, 3);
        List<Double> doubles = Arrays.asList(1.1, 2.2, 3.3);

        System.out.println(sumOfNumbers(integers));  // 6.0
        System.out.println(sumOfNumbers(doubles));   // 6.6
    }
}
```

**下界通配符 (? super)：**
```java
public class SuperWildcardDemo {
    // 只能写入，读取时只能赋值给Object
    public static void addNumbers(List<? super Integer> list) {
        list.add(1);
        list.add(2);
        list.add(3);
        // Integer num = list.get(0);  // 编译错误
        Object obj = list.get(0);     // 只能赋值给Object
    }

    public static void main(String[] args) {
        List<Number> numbers = new ArrayList<>();
        List<Object> objects = new ArrayList<>();

        addNumbers(numbers);
        addNumbers(objects);

        System.out.println(numbers);  // [1, 2, 3]
        System.out.println(objects);  // [1, 2, 3]
    }
}
```

**无界通配符 (?)：**
```java
public class UnboundedWildcardDemo {
    public static void printList(List<?> list) {
        for (Object item : list) {
            System.out.println(item);
        }
    }

    public static int getSize(List<?> list) {
        return list.size();
    }
}
```

### 🎯 枚举 (Enums)

#### 9.1 枚举基础概念
**核心定义：**
- 使用`enum`关键字定义的特殊类
- 用于定义一组固定的常量
- 每个枚举值都是该枚举类型的实例
- 提供类型安全的常量定义方式

**基础语法：**
```java
public enum Season {
    SPRING, SUMMER, AUTUMN, WINTER
}

public enum Color {
    RED, GREEN, BLUE, YELLOW, BLACK, WHITE
}
```

**枚举的使用：**
```java
public class EnumDemo {
    public static void main(String[] args) {
        // 枚举变量声明和赋值
        Season season = Season.SPRING;
        Color color = Color.RED;

        // 枚举比较
        if (season == Season.SPRING) {
            System.out.println("春天来了");
        }

        // switch语句中使用枚举
        switch (season) {
            case SPRING:
                System.out.println("春暖花开");
                break;
            case SUMMER:
                System.out.println("夏日炎炎");
                break;
            case AUTUMN:
                System.out.println("秋高气爽");
                break;
            case WINTER:
                System.out.println("冬雪纷飞");
                break;
        }
    }
}
```

#### 9.2 枚举的常用方法
**内置方法：**
```java
public class EnumMethodDemo {
    public static void main(String[] args) {
        Season season = Season.SUMMER;

        // name()：返回枚举常量的名称
        System.out.println(season.name());  // SUMMER

        // ordinal()：返回枚举常量的序号（从0开始）
        System.out.println(season.ordinal());  // 1

        // toString()：返回枚举常量的字符串表示
        System.out.println(season.toString());  // SUMMER

        // values()：返回所有枚举常量的数组
        Season[] seasons = Season.values();
        for (Season s : seasons) {
            System.out.println(s.name() + " - " + s.ordinal());
        }

        // valueOf()：根据名称获取枚举常量
        Season spring = Season.valueOf("SPRING");
        System.out.println(spring);  // SPRING
    }
}
```

#### 9.3 带参数的枚举
**枚举构造器和成员：**
```java
public enum Planet {
    MERCURY(3.303e+23, 2.4397e6),
    VENUS(4.869e+24, 6.0518e6),
    EARTH(5.976e+24, 6.37814e6),
    MARS(6.421e+23, 3.3972e6);

    private final double mass;    // 质量（千克）
    private final double radius;  // 半径（米）

    // 枚举构造器必须是private
    Planet(double mass, double radius) {
        this.mass = mass;
        this.radius = radius;
    }

    public double getMass() {
        return mass;
    }

    public double getRadius() {
        return radius;
    }

    // 计算表面重力
    public double surfaceGravity() {
        final double G = 6.67300E-11;
        return G * mass / (radius * radius);
    }

    // 计算在该星球上的重量
    public double surfaceWeight(double otherMass) {
        return otherMass * surfaceGravity();
    }
}
```

**使用带参数的枚举：**
```java
public class PlanetDemo {
    public static void main(String[] args) {
        double earthWeight = 70.0;  // 地球上的重量（千克）

        for (Planet planet : Planet.values()) {
            double weight = planet.surfaceWeight(earthWeight);
            System.out.printf("在%s上的重量: %.2f kg%n",
                            planet.name(), weight);
        }
    }
}
```

#### 9.4 枚举实现接口
**枚举实现接口：**
```java
public interface Describable {
    String getDescription();
}

public enum Status implements Describable {
    PENDING("等待处理"),
    PROCESSING("正在处理"),
    COMPLETED("已完成"),
    FAILED("处理失败");

    private final String description;

    Status(String description) {
        this.description = description;
    }

    @Override
    public String getDescription() {
        return description;
    }

    // 根据描述查找枚举
    public static Status findByDescription(String desc) {
        for (Status status : values()) {
            if (status.description.equals(desc)) {
                return status;
            }
        }
        return null;
    }
}
```

### 📊 Day3核心知识点总结

#### 🎯 内部类要点
1. **成员内部类**：可访问外部类所有成员，需要外部类对象才能创建
2. **静态内部类**：独立于外部类对象，只能访问外部类静态成员
3. **局部内部类**：定义在方法内，只能访问final或effectively final变量
4. **匿名内部类**：没有名字，常用于接口实现和事件处理
5. **应用场景**：GUI事件处理、回调函数、辅助类实现

#### 🎯 泛型要点
1. **类型安全**：编译时检查类型，避免ClassCastException
2. **消除转换**：无需显式类型转换，提高代码可读性
3. **代码复用**：一套代码处理多种类型
4. **通配符**：? extends（上界）、? super（下界）、?（无界）
5. **类型擦除**：运行时泛型信息被擦除，保持向后兼容

#### 🎯 枚举要点
1. **类型安全**：编译时检查，避免使用无效常量
2. **单例保证**：每个枚举值都是单例
3. **功能丰富**：可以有构造器、方法、字段
4. **接口实现**：枚举可以实现接口
5. **应用场景**：状态机、配置选项、常量定义

#### 🎯 设计模式应用
- **单例模式**：枚举实现单例（最佳实践）
- **策略模式**：枚举实现不同策略
- **工厂模式**：泛型工厂方法
- **观察者模式**：匿名内部类实现监听器

---

## 🛠️ Day4: 常用API与工具类

### 📌 核心学习目标
- 掌握String类的常用方法和性能优化
- 熟练运用StringBuilder和StringJoiner
- 理解包装类的自动装箱和拆箱机制
- 掌握JDK8时间API的使用方法
- 了解System和Runtime类的系统操作

### 📝 字符串处理 (String Processing)

#### 10.1 String类核心特性
**不可变性 (Immutability)：**
```java
public class StringImmutableDemo {
    public static void main(String[] args) {
        String str1 = "Hello";
        String str2 = str1;

        str1 = str1 + " World";  // 创建新对象，原对象不变

        System.out.println(str1);  // Hello World
        System.out.println(str2);  // Hello（原对象未改变）

        // 字符串常量池
        String s1 = "Java";
        String s2 = "Java";
        String s3 = new String("Java");

        System.out.println(s1 == s2);        // true（指向同一对象）
        System.out.println(s1 == s3);        // false（不同对象）
        System.out.println(s1.equals(s3));   // true（内容相同）
    }
}
```

#### 10.2 String常用方法
**字符串判断方法：**
```java
public class StringMethodDemo {
    public static void main(String[] args) {
        String str = "Hello World Java";

        // 长度和判空
        System.out.println(str.length());        // 17
        System.out.println(str.isEmpty());       // false
        System.out.println("".isEmpty());        // true

        // 字符和子串判断
        System.out.println(str.charAt(6));       // W
        System.out.println(str.contains("World")); // true
        System.out.println(str.startsWith("Hello")); // true
        System.out.println(str.endsWith("Java"));    // true

        // 位置查找
        System.out.println(str.indexOf("o"));        // 4（第一次出现）
        System.out.println(str.lastIndexOf("o"));    // 7（最后一次出现）
        System.out.println(str.indexOf("Python"));   // -1（未找到）
    }
}
```

**字符串转换方法：**
```java
public class StringConvertDemo {
    public static void main(String[] args) {
        String str = "  Hello World Java  ";

        // 大小写转换
        System.out.println(str.toUpperCase());   // "  HELLO WORLD JAVA  "
        System.out.println(str.toLowerCase());   // "  hello world java  "

        // 去除空白
        System.out.println(str.trim());          // "Hello World Java"
        System.out.println(str.strip());         // "Hello World Java"（JDK11+）

        // 替换操作
        System.out.println(str.replace("World", "Java"));     // 替换所有
        System.out.println(str.replaceFirst("l", "L"));       // 替换第一个
        System.out.println(str.replaceAll("\\s+", "-"));      // 正则替换

        // 分割操作
        String[] words = str.trim().split(" ");
        System.out.println(Arrays.toString(words));  // [Hello, World, Java]

        // 截取操作
        System.out.println(str.substring(7));        // "World Java  "
        System.out.println(str.substring(7, 12));    // "World"
    }
}
```

#### 10.3 StringBuilder性能优化
**StringBuilder vs String：**
```java
public class StringBuilderDemo {
    public static void main(String[] args) {
        // 低效的字符串拼接
        long start1 = System.currentTimeMillis();
        String result1 = "";
        for (int i = 0; i < 10000; i++) {
            result1 += i;  // 每次都创建新对象
        }
        long end1 = System.currentTimeMillis();
        System.out.println("String拼接耗时：" + (end1 - start1) + "ms");

        // 高效的字符串拼接
        long start2 = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            sb.append(i);  // 在内部缓冲区操作
        }
        String result2 = sb.toString();
        long end2 = System.currentTimeMillis();
        System.out.println("StringBuilder拼接耗时：" + (end2 - start2) + "ms");
    }
}
```

**StringBuilder常用方法：**
```java
public class StringBuilderMethodDemo {
    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder("Hello");

        // 追加操作
        sb.append(" World");
        sb.append(123);
        sb.append(true);
        System.out.println(sb);  // Hello World123true

        // 插入操作
        sb.insert(5, " Java");
        System.out.println(sb);  // Hello Java World123true

        // 删除操作
        sb.delete(5, 10);        // 删除指定范围
        sb.deleteCharAt(5);      // 删除指定位置字符
        System.out.println(sb);  // HelloWorld123true

        // 替换操作
        sb.replace(0, 5, "Hi");
        System.out.println(sb);  // HiWorld123true

        // 反转操作
        sb.reverse();
        System.out.println(sb);  // eurt321dlroWiH

        // 容量管理
        System.out.println("长度：" + sb.length());
        System.out.println("容量：" + sb.capacity());
    }
}
```

#### 10.4 StringJoiner字符串连接
**StringJoiner基础用法：**
```java
import java.util.StringJoiner;

public class StringJoinerDemo {
    public static void main(String[] args) {
        // 基础用法
        StringJoiner sj1 = new StringJoiner(", ");
        sj1.add("Apple");
        sj1.add("Banana");
        sj1.add("Orange");
        System.out.println(sj1);  // Apple, Banana, Orange

        // 带前缀和后缀
        StringJoiner sj2 = new StringJoiner(", ", "[", "]");
        sj2.add("Java");
        sj2.add("Python");
        sj2.add("C++");
        System.out.println(sj2);  // [Java, Python, C++]

        // 合并StringJoiner
        StringJoiner sj3 = new StringJoiner(" | ");
        sj3.add("First");
        sj3.add("Second");

        StringJoiner sj4 = new StringJoiner(" | ");
        sj4.add("Third");
        sj4.add("Fourth");

        sj3.merge(sj4);
        System.out.println(sj3);  // First | Second | Third | Fourth
    }
}
```

### 📦 包装类 (Wrapper Classes)

#### 11.1 基本类型与包装类对应
**对应关系：**
```java
// 基本类型    包装类
byte      -> Byte
short     -> Short
int       -> Integer
long      -> Long
float     -> Float
double    -> Double
char      -> Character
boolean   -> Boolean
```

#### 11.2 自动装箱和拆箱
**装箱拆箱机制：**
```java
public class AutoBoxingDemo {
    public static void main(String[] args) {
        // 自动装箱：基本类型 → 包装类
        Integer i1 = 100;           // 等价于 Integer.valueOf(100)
        Double d1 = 3.14;           // 等价于 Double.valueOf(3.14)
        Boolean b1 = true;          // 等价于 Boolean.valueOf(true)

        // 自动拆箱：包装类 → 基本类型
        int i2 = i1;                // 等价于 i1.intValue()
        double d2 = d1;             // 等价于 d1.doubleValue()
        boolean b2 = b1;            // 等价于 b1.booleanValue()

        // 混合运算
        Integer a = 10;
        Integer b = 20;
        Integer c = a + b;          // 自动拆箱运算后装箱
        System.out.println(c);      // 30
    }
}
```

**缓存机制：**
```java
public class WrapperCacheDemo {
    public static void main(String[] args) {
        // Integer缓存 -128 到 127
        Integer i1 = 127;
        Integer i2 = 127;
        Integer i3 = 128;
        Integer i4 = 128;

        System.out.println(i1 == i2);  // true（缓存范围内）
        System.out.println(i3 == i4);  // false（超出缓存范围）

        // 其他包装类的缓存
        Boolean b1 = true;
        Boolean b2 = true;
        System.out.println(b1 == b2);  // true（Boolean缓存true/false）

        Character c1 = 'A';
        Character c2 = 'A';
        System.out.println(c1 == c2);  // true（Character缓存0-127）
    }
}
```

### ⏰ JDK8时间API (Time API)

#### 12.1 LocalDate、LocalTime、LocalDateTime
**基础时间类：**
```java
import java.time.*;
import java.time.format.DateTimeFormatter;

public class LocalDateTimeDemo {
    public static void main(String[] args) {
        // LocalDate：日期（年月日）
        LocalDate date = LocalDate.now();
        System.out.println("当前日期：" + date);  // 2024-07-29

        LocalDate specificDate = LocalDate.of(2024, 12, 25);
        System.out.println("指定日期：" + specificDate);  // 2024-12-25

        // LocalTime：时间（时分秒）
        LocalTime time = LocalTime.now();
        System.out.println("当前时间：" + time);  // 14:30:25.123

        LocalTime specificTime = LocalTime.of(9, 30, 0);
        System.out.println("指定时间：" + specificTime);  // 09:30

        // LocalDateTime：日期时间
        LocalDateTime dateTime = LocalDateTime.now();
        System.out.println("当前日期时间：" + dateTime);

        LocalDateTime specificDateTime = LocalDateTime.of(2024, 7, 29, 14, 30, 0);
        System.out.println("指定日期时间：" + specificDateTime);
    }
}
```

#### 12.2 时间格式化和解析
**DateTimeFormatter使用：**
```java
public class DateTimeFormatterDemo {
    public static void main(String[] args) {
        LocalDateTime now = LocalDateTime.now();

        // 预定义格式
        System.out.println(now.format(DateTimeFormatter.ISO_LOCAL_DATE));      // 2024-07-29
        System.out.println(now.format(DateTimeFormatter.ISO_LOCAL_TIME));      // 14:30:25.123
        System.out.println(now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)); // 2024-07-29T14:30:25.123

        // 自定义格式
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        System.out.println(now.format(formatter1));  // 2024-07-29 14:30:25

        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH时mm分ss秒");
        System.out.println(now.format(formatter2));  // 2024年07月29日 14时30分25秒

        // 解析字符串为时间
        String timeStr = "2024-12-25 09:30:00";
        LocalDateTime parsed = LocalDateTime.parse(timeStr, formatter1);
        System.out.println("解析结果：" + parsed);
    }
}
```

#### 12.3 时间计算和比较
**时间运算：**
```java
public class TimeCalculationDemo {
    public static void main(String[] args) {
        LocalDateTime now = LocalDateTime.now();

        // 加减操作
        LocalDateTime future = now.plusYears(1)
                                 .plusMonths(2)
                                 .plusDays(3)
                                 .plusHours(4)
                                 .plusMinutes(5);
        System.out.println("未来时间：" + future);

        LocalDateTime past = now.minusYears(1)
                               .minusMonths(2)
                               .minusDays(3);
        System.out.println("过去时间：" + past);

        // 时间比较
        LocalDate date1 = LocalDate.of(2024, 7, 29);
        LocalDate date2 = LocalDate.of(2024, 12, 25);

        System.out.println(date1.isBefore(date2));  // true
        System.out.println(date1.isAfter(date2));   // false
        System.out.println(date1.isEqual(date2));   // false

        // 时间间隔
        Period period = Period.between(date1, date2);
        System.out.println("相差：" + period.getMonths() + "个月" + period.getDays() + "天");

        Duration duration = Duration.between(now.toLocalTime(), LocalTime.of(18, 0));
        System.out.println("距离18点还有：" + duration.toHours() + "小时");
    }
}
```

### 🖥️ 系统相关类

#### 13.1 System类
**系统操作：**
```java
public class SystemDemo {
    public static void main(String[] args) {
        // 获取系统属性
        System.out.println("Java版本：" + System.getProperty("java.version"));
        System.out.println("操作系统：" + System.getProperty("os.name"));
        System.out.println("用户目录：" + System.getProperty("user.home"));
        System.out.println("当前目录：" + System.getProperty("user.dir"));

        // 时间相关
        long currentTime = System.currentTimeMillis();
        System.out.println("当前时间戳：" + currentTime);

        long nanoTime = System.nanoTime();
        System.out.println("纳秒时间：" + nanoTime);

        // 数组复制
        int[] source = {1, 2, 3, 4, 5};
        int[] target = new int[5];
        System.arraycopy(source, 0, target, 0, source.length);
        System.out.println("复制结果：" + Arrays.toString(target));

        // 垃圾回收
        System.gc();  // 建议JVM进行垃圾回收

        // 程序退出
        // System.exit(0);  // 正常退出
    }
}
```

#### 13.2 Runtime类
**运行时环境操作：**
```java
public class RuntimeDemo {
    public static void main(String[] args) {
        Runtime runtime = Runtime.getRuntime();

        // 系统信息
        System.out.println("可用处理器数量：" + runtime.availableProcessors());
        System.out.println("JVM总内存：" + runtime.totalMemory() / 1024 / 1024 + "MB");
        System.out.println("JVM空闲内存：" + runtime.freeMemory() / 1024 / 1024 + "MB");
        System.out.println("JVM最大内存：" + runtime.maxMemory() / 1024 / 1024 + "MB");

        // 执行系统命令
        try {
            Process process = runtime.exec("notepad");  // Windows记事本
            // Process process = runtime.exec("gedit");  // Linux文本编辑器
            System.out.println("程序已启动");
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 添加关闭钩子
        runtime.addShutdownHook(new Thread(() -> {
            System.out.println("程序即将关闭，执行清理操作...");
        }));
    }
}
```

### 📊 Day4核心知识点总结

#### 🎯 字符串处理要点
1. **String不可变性**：每次修改都创建新对象，注意性能问题
2. **StringBuilder优化**：大量字符串拼接时使用，避免性能损失
3. **StringJoiner连接**：专门用于字符串连接，支持分隔符和前后缀
4. **常用方法**：length()、charAt()、substring()、split()、replace()等
5. **字符串池**：字面量字符串存储在常量池中，节省内存

#### 🎯 包装类要点
1. **自动装箱拆箱**：基本类型与包装类自动转换
2. **缓存机制**：小范围数值使用缓存对象，注意==比较
3. **类型转换**：包装类提供丰富的类型转换方法
4. **null安全**：包装类可以为null，注意空指针异常
5. **性能考虑**：频繁装箱拆箱影响性能，合理使用基本类型

#### 🎯 时间API要点
1. **线程安全**：JDK8时间类都是不可变且线程安全的
2. **类型明确**：LocalDate（日期）、LocalTime（时间）、LocalDateTime（日期时间）
3. **格式化解析**：DateTimeFormatter提供灵活的格式化功能
4. **时间计算**：plus/minus方法进行时间运算，Period/Duration计算间隔
5. **时区处理**：ZonedDateTime处理时区相关操作

#### 🎯 系统类要点
1. **System类**：提供系统级操作，如属性获取、时间戳、数组复制
2. **Runtime类**：运行时环境信息，如内存状态、处理器数量
3. **进程控制**：可以启动外部程序和添加关闭钩子
4. **性能监控**：获取内存使用情况，进行性能分析
5. **系统交互**：与操作系统进行交互，执行系统命令

---

## 🚀 Day5: Lambda表达式与函数式编程

### 📌 核心学习目标
- 理解Lambda表达式的语法和使用场景
- 掌握函数式接口的概念和应用
- 熟练运用方法引用简化代码
- 理解函数式编程思想
- 掌握正则表达式的基本用法

### 🔗 Lambda表达式 (Lambda Expressions)

#### 14.1 Lambda基础语法
**语法格式：**
```
(参数列表) -> {方法体}
```

**基础示例：**
```java
import java.util.*;
import java.util.function.*;

public class LambdaBasicDemo {
    public static void main(String[] args) {
        // 传统匿名内部类
        Runnable r1 = new Runnable() {
            @Override
            public void run() {
                System.out.println("传统方式");
            }
        };

        // Lambda表达式
        Runnable r2 = () -> System.out.println("Lambda方式");

        // 带参数的Lambda
        Comparator<String> comp1 = (s1, s2) -> s1.compareTo(s2);

        // 多行Lambda
        Comparator<String> comp2 = (s1, s2) -> {
            System.out.println("比较：" + s1 + " vs " + s2);
            return s1.compareTo(s2);
        };

        r1.run();
        r2.run();
    }
}
```

#### 14.2 函数式接口 (Functional Interfaces)
**自定义函数式接口：**
```java
@FunctionalInterface
public interface Calculator {
    int calculate(int a, int b);

    // 可以有默认方法
    default void printResult(int result) {
        System.out.println("计算结果：" + result);
    }

    // 可以有静态方法
    static void info() {
        System.out.println("这是一个计算器接口");
    }
}

public class FunctionalInterfaceDemo {
    public static void main(String[] args) {
        // 使用Lambda实现函数式接口
        Calculator add = (a, b) -> a + b;
        Calculator multiply = (a, b) -> a * b;
        Calculator subtract = (a, b) -> a - b;

        System.out.println("加法：" + add.calculate(10, 5));      // 15
        System.out.println("乘法：" + multiply.calculate(10, 5)); // 50
        System.out.println("减法：" + subtract.calculate(10, 5)); // 5

        add.printResult(add.calculate(10, 5));
        Calculator.info();
    }
}
```

#### 14.3 常用函数式接口
**JDK内置函数式接口：**
```java
import java.util.function.*;

public class BuiltInFunctionalDemo {
    public static void main(String[] args) {
        // Predicate<T>：断言型接口，接收T返回boolean
        Predicate<String> isEmpty = s -> s.isEmpty();
        Predicate<Integer> isEven = n -> n % 2 == 0;

        System.out.println(isEmpty.test(""));      // true
        System.out.println(isEven.test(4));        // true

        // Consumer<T>：消费型接口，接收T无返回值
        Consumer<String> printer = s -> System.out.println("打印：" + s);
        Consumer<List<String>> listPrinter = list -> list.forEach(System.out::println);

        printer.accept("Hello World");

        // Supplier<T>：供给型接口，无参数返回T
        Supplier<String> stringSupplier = () -> "Hello Lambda";
        Supplier<Double> randomSupplier = () -> Math.random();

        System.out.println(stringSupplier.get());  // Hello Lambda
        System.out.println(randomSupplier.get());  // 随机数

        // Function<T,R>：函数型接口，接收T返回R
        Function<String, Integer> lengthFunction = s -> s.length();
        Function<Integer, String> toStringFunction = n -> "数字：" + n;

        System.out.println(lengthFunction.apply("Hello"));     // 5
        System.out.println(toStringFunction.apply(123));       // 数字：123

        // 组合使用
        Function<String, String> upperCase = s -> s.toUpperCase();
        Function<String, String> addPrefix = s -> "PREFIX_" + s;
        Function<String, String> combined = upperCase.andThen(addPrefix);

        System.out.println(combined.apply("hello"));  // PREFIX_HELLO
    }
}
```

### 🔗 方法引用 (Method References)

#### 15.1 静态方法引用
**语法：类名::静态方法名**
```java
import java.util.*;

public class StaticMethodRefDemo {
    public static void main(String[] args) {
        List<String> numbers = Arrays.asList("3", "1", "4", "1", "5");

        // Lambda表达式
        numbers.sort((s1, s2) -> Integer.compare(Integer.parseInt(s1), Integer.parseInt(s2)));

        // 静态方法引用
        List<String> numbers2 = Arrays.asList("3", "1", "4", "1", "5");
        numbers2.sort(Comparator.comparing(Integer::parseInt));

        System.out.println(numbers2);  // [1, 1, 3, 4, 5]

        // 更多静态方法引用示例
        List<Integer> nums = Arrays.asList(1, 2, 3, 4, 5);
        nums.forEach(System.out::println);  // 等价于 n -> System.out.println(n)

        // 自定义静态方法引用
        List<String> strings = Arrays.asList("apple", "banana", "cherry");
        strings.forEach(StaticMethodRefDemo::printWithPrefix);
    }

    public static void printWithPrefix(String s) {
        System.out.println("Item: " + s);
    }
}
```

#### 15.2 实例方法引用
**语法：对象名::实例方法名**
```java
public class InstanceMethodRefDemo {
    public void processString(String s) {
        System.out.println("处理字符串：" + s.toUpperCase());
    }

    public static void main(String[] args) {
        InstanceMethodRefDemo demo = new InstanceMethodRefDemo();
        List<String> strings = Arrays.asList("hello", "world", "java");

        // Lambda表达式
        strings.forEach(s -> demo.processString(s));

        // 实例方法引用
        strings.forEach(demo::processString);

        // 特定类型的任意对象的实例方法引用
        List<String> words = Arrays.asList("apple", "banana", "cherry");
        words.sort(String::compareToIgnoreCase);  // 等价于 (s1, s2) -> s1.compareToIgnoreCase(s2)

        System.out.println(words);
    }
}
```

#### 15.3 构造器引用
**语法：类名::new**
```java
import java.util.*;
import java.util.function.*;

class Person {
    private String name;
    private int age;

    public Person() {
        this.name = "Unknown";
        this.age = 0;
    }

    public Person(String name) {
        this.name = name;
        this.age = 0;
    }

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    @Override
    public String toString() {
        return "Person{name='" + name + "', age=" + age + "}";
    }
}

public class ConstructorRefDemo {
    public static void main(String[] args) {
        // 无参构造器引用
        Supplier<Person> personSupplier = Person::new;
        Person p1 = personSupplier.get();
        System.out.println(p1);

        // 单参构造器引用
        Function<String, Person> personFunction = Person::new;
        Person p2 = personFunction.apply("张三");
        System.out.println(p2);

        // 双参构造器引用
        BiFunction<String, Integer, Person> personBiFunction = Person::new;
        Person p3 = personBiFunction.apply("李四", 25);
        System.out.println(p3);

        // 数组构造器引用
        Function<Integer, String[]> arrayFunction = String[]::new;
        String[] array = arrayFunction.apply(5);
        System.out.println("数组长度：" + array.length);
    }
}
```

### 📝 正则表达式 (Regular Expressions)

#### 16.1 正则表达式基础
**基本语法：**
```java
import java.util.regex.*;

public class RegexBasicDemo {
    public static void main(String[] args) {
        // 基本匹配
        String text = "Hello123World456";

        // 匹配数字
        Pattern digitPattern = Pattern.compile("\\d+");
        Matcher digitMatcher = digitPattern.matcher(text);

        while (digitMatcher.find()) {
            System.out.println("找到数字：" + digitMatcher.group());
        }

        // 匹配字母
        Pattern letterPattern = Pattern.compile("[a-zA-Z]+");
        Matcher letterMatcher = letterPattern.matcher(text);

        while (letterMatcher.find()) {
            System.out.println("找到字母：" + letterMatcher.group());
        }

        // 简单验证
        String email = "<EMAIL>";
        boolean isValidEmail = email.matches("\\w+@\\w+\\.\\w+");
        System.out.println("邮箱格式正确：" + isValidEmail);
    }
}
```

#### 16.2 常用正则表达式
**实用正则模式：**
```java
public class CommonRegexDemo {
    public static void main(String[] args) {
        // 手机号验证
        String phone1 = "13812345678";
        String phone2 = "12345678901";
        String phoneRegex = "1[3-9]\\d{9}";

        System.out.println(phone1 + " 是否为有效手机号：" + phone1.matches(phoneRegex));
        System.out.println(phone2 + " 是否为有效手机号：" + phone2.matches(phoneRegex));

        // 邮箱验证
        String email1 = "<EMAIL>";
        String email2 = "invalid.email";
        String emailRegex = "\\w+@\\w+\\.\\w+";

        System.out.println(email1 + " 是否为有效邮箱：" + email1.matches(emailRegex));
        System.out.println(email2 + " 是否为有效邮箱：" + email2.matches(emailRegex));

        // 身份证号验证（简化版）
        String idCard1 = "110101199001011234";
        String idCard2 = "12345678901234567X";
        String idCardRegex = "\\d{17}[\\dX]";

        System.out.println(idCard1 + " 是否为有效身份证：" + idCard1.matches(idCardRegex));
        System.out.println(idCard2 + " 是否为有效身份证：" + idCard2.matches(idCardRegex));

        // 密码强度验证（至少8位，包含字母和数字）
        String password1 = "abc12345";
        String password2 = "12345678";
        String passwordRegex = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$";

        System.out.println(password1 + " 密码强度合格：" + password1.matches(passwordRegex));
        System.out.println(password2 + " 密码强度合格：" + password2.matches(passwordRegex));
    }
}
```

### 📊 Day5核心知识点总结

#### 🎯 Lambda表达式要点
1. **语法简洁**：(参数) -> {方法体}，简化匿名内部类
2. **函数式接口**：只有一个抽象方法的接口，可用Lambda实现
3. **类型推断**：编译器自动推断参数类型
4. **闭包特性**：可以访问外部final或effectively final变量
5. **应用场景**：集合操作、事件处理、并行计算

#### 🎯 方法引用要点
1. **静态方法引用**：类名::静态方法名
2. **实例方法引用**：对象名::实例方法名
3. **类型方法引用**：类名::实例方法名
4. **构造器引用**：类名::new
5. **简化代码**：当Lambda只是调用现有方法时使用

#### 🎯 函数式编程要点
1. **不可变性**：避免修改外部状态
2. **纯函数**：相同输入产生相同输出，无副作用
3. **高阶函数**：接收或返回函数的函数
4. **组合性**：函数可以组合形成更复杂的功能
5. **声明式**：关注做什么而不是怎么做

#### 🎯 正则表达式要点
1. **模式匹配**：用于字符串的模式匹配和验证
2. **常用元字符**：\\d（数字）、\\w（字母数字）、\\s（空白）
3. **量词**：+（一个或多个）、*（零个或多个）、?（零个或一个）
4. **分组**：()用于分组，[]用于字符类
5. **应用场景**：数据验证、文本处理、信息提取

---

## ⚠️ Day6: 异常处理与集合基础

### 📌 核心学习目标
- 理解异常处理机制和异常体系结构
- 掌握try-catch-finally语句的使用
- 熟练运用Collection集合框架
- 理解List、Set接口的特点和应用
- 掌握集合的遍历和常用操作

### 🚨 异常处理机制 (Exception Handling)

#### 17.1 异常体系结构
**异常继承体系：**
```
Throwable
├── Error（错误）
│   ├── OutOfMemoryError
│   ├── StackOverflowError
│   └── ...
└── Exception（异常）
    ├── RuntimeException（运行时异常）
    │   ├── NullPointerException
    │   ├── ArrayIndexOutOfBoundsException
    │   ├── ClassCastException
    │   └── ...
    └── 编译时异常
        ├── IOException
        ├── ParseException
        ├── ClassNotFoundException
        └── ...
```

**异常分类示例：**
```java
import java.io.*;
import java.text.*;
import java.util.*;

public class ExceptionTypesDemo {
    public static void main(String[] args) {
        // 运行时异常（RuntimeException）
        demonstrateRuntimeExceptions();

        // 编译时异常（Checked Exception）
        demonstrateCheckedExceptions();
    }

    public static void demonstrateRuntimeExceptions() {
        try {
            // 空指针异常
            String str = null;
            // System.out.println(str.length());  // NullPointerException

            // 数组越界异常
            int[] arr = {1, 2, 3};
            // System.out.println(arr[5]);  // ArrayIndexOutOfBoundsException

            // 类型转换异常
            Object obj = "Hello";
            // Integer num = (Integer) obj;  // ClassCastException

            // 算术异常
            // int result = 10 / 0;  // ArithmeticException

            System.out.println("运行时异常演示完成");
        } catch (Exception e) {
            System.out.println("捕获异常：" + e.getClass().getSimpleName());
        }
    }

    public static void demonstrateCheckedExceptions() {
        try {
            // 文件操作异常
            FileInputStream fis = new FileInputStream("nonexistent.txt");

            // 日期解析异常
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = sdf.parse("invalid-date");

        } catch (FileNotFoundException e) {
            System.out.println("文件未找到：" + e.getMessage());
        } catch (ParseException e) {
            System.out.println("日期解析错误：" + e.getMessage());
        }
    }
}
```

#### 17.2 try-catch-finally语句
**基础语法：**
```java
public class TryCatchFinallyDemo {
    public static void main(String[] args) {
        // 基本try-catch
        try {
            int result = 10 / 0;
        } catch (ArithmeticException e) {
            System.out.println("算术异常：" + e.getMessage());
        }

        // 多重catch
        try {
            String str = null;
            int[] arr = {1, 2, 3};

            System.out.println(str.length());  // 可能抛出NullPointerException
            System.out.println(arr[5]);        // 可能抛出ArrayIndexOutOfBoundsException

        } catch (NullPointerException e) {
            System.out.println("空指针异常：" + e.getMessage());
        } catch (ArrayIndexOutOfBoundsException e) {
            System.out.println("数组越界异常：" + e.getMessage());
        } catch (Exception e) {
            System.out.println("其他异常：" + e.getMessage());
        }

        // try-catch-finally
        try {
            System.out.println("执行try块");
            int result = 10 / 2;
            System.out.println("计算结果：" + result);
        } catch (Exception e) {
            System.out.println("执行catch块");
        } finally {
            System.out.println("执行finally块（总是执行）");
        }
    }
}
```

#### 17.3 异常处理最佳实践
**自定义异常：**
```java
// 自定义异常类
class CustomException extends Exception {
    public CustomException(String message) {
        super(message);
    }

    public CustomException(String message, Throwable cause) {
        super(message, cause);
    }
}

class BankAccount {
    private double balance;

    public BankAccount(double balance) {
        this.balance = balance;
    }

    public void withdraw(double amount) throws CustomException {
        if (amount <= 0) {
            throw new CustomException("提取金额必须大于0");
        }
        if (amount > balance) {
            throw new CustomException("余额不足，当前余额：" + balance);
        }
        balance -= amount;
        System.out.println("成功提取：" + amount + "，余额：" + balance);
    }

    public double getBalance() {
        return balance;
    }
}

public class CustomExceptionDemo {
    public static void main(String[] args) {
        BankAccount account = new BankAccount(1000);

        try {
            account.withdraw(500);   // 正常提取
            account.withdraw(600);   // 余额不足
        } catch (CustomException e) {
            System.out.println("操作失败：" + e.getMessage());
        }

        // try-with-resources（自动资源管理）
        try (Scanner scanner = new Scanner(System.in)) {
            System.out.println("请输入内容：");
            // String input = scanner.nextLine();
        } catch (Exception e) {
            System.out.println("输入异常：" + e.getMessage());
        }
        // scanner会自动关闭
    }
}
```

### 📚 Collection集合框架

#### 18.1 集合框架概述
**集合体系结构：**
```
Collection（接口）
├── List（接口）- 有序、可重复
│   ├── ArrayList（类）
│   ├── LinkedList（类）
│   └── Vector（类）
└── Set（接口）- 无序、不可重复
    ├── HashSet（类）
    ├── LinkedHashSet（类）
    └── TreeSet（类）

Map（接口）- 键值对
├── HashMap（类）
├── LinkedHashMap（类）
├── TreeMap（类）
└── Hashtable（类）
```

#### 18.2 List接口及实现类
**ArrayList使用：**
```java
import java.util.*;

public class ArrayListDemo {
    public static void main(String[] args) {
        // 创建ArrayList
        List<String> list = new ArrayList<>();

        // 添加元素
        list.add("Apple");
        list.add("Banana");
        list.add("Cherry");
        list.add("Apple");  // 允许重复

        System.out.println("列表内容：" + list);
        System.out.println("列表大小：" + list.size());

        // 插入元素
        list.add(1, "Orange");
        System.out.println("插入后：" + list);

        // 获取元素
        System.out.println("第一个元素：" + list.get(0));
        System.out.println("最后一个元素：" + list.get(list.size() - 1));

        // 查找元素
        System.out.println("Apple的位置：" + list.indexOf("Apple"));
        System.out.println("Apple最后位置：" + list.lastIndexOf("Apple"));
        System.out.println("是否包含Banana：" + list.contains("Banana"));

        // 修改元素
        list.set(0, "Grape");
        System.out.println("修改后：" + list);

        // 删除元素
        list.remove("Banana");           // 按值删除
        list.remove(0);                  // 按索引删除
        System.out.println("删除后：" + list);

        // 遍历方式
        System.out.println("\n遍历方式：");

        // 1. 传统for循环
        for (int i = 0; i < list.size(); i++) {
            System.out.println("索引" + i + "：" + list.get(i));
        }

        // 2. 增强for循环
        for (String item : list) {
            System.out.println("元素：" + item);
        }

        // 3. Iterator迭代器
        Iterator<String> iterator = list.iterator();
        while (iterator.hasNext()) {
            System.out.println("迭代：" + iterator.next());
        }

        // 4. Lambda表达式
        list.forEach(item -> System.out.println("Lambda：" + item));
        list.forEach(System.out::println);  // 方法引用
    }
}
```

**LinkedList使用：**
```java
import java.util.*;

public class LinkedListDemo {
    public static void main(String[] args) {
        LinkedList<String> linkedList = new LinkedList<>();

        // LinkedList特有方法
        linkedList.addFirst("First");
        linkedList.addLast("Last");
        linkedList.add("Middle");

        System.out.println("链表内容：" + linkedList);

        // 获取首尾元素
        System.out.println("第一个元素：" + linkedList.getFirst());
        System.out.println("最后一个元素：" + linkedList.getLast());

        // 删除首尾元素
        String first = linkedList.removeFirst();
        String last = linkedList.removeLast();

        System.out.println("删除的首元素：" + first);
        System.out.println("删除的尾元素：" + last);
        System.out.println("删除后：" + linkedList);

        // 队列操作
        linkedList.offer("Queue1");  // 入队
        linkedList.offer("Queue2");

        System.out.println("队列状态：" + linkedList);

        String polled = linkedList.poll();  // 出队
        System.out.println("出队元素：" + polled);
        System.out.println("出队后：" + linkedList);

        // 栈操作
        linkedList.push("Stack1");  // 入栈
        linkedList.push("Stack2");

        System.out.println("栈状态：" + linkedList);

        String popped = linkedList.pop();  // 出栈
        System.out.println("出栈元素：" + popped);
        System.out.println("出栈后：" + linkedList);
    }
}
```

#### 18.3 Set接口及实现类
**HashSet使用：**
```java
import java.util.*;

public class HashSetDemo {
    public static void main(String[] args) {
        Set<String> hashSet = new HashSet<>();

        // 添加元素
        hashSet.add("Apple");
        hashSet.add("Banana");
        hashSet.add("Cherry");
        hashSet.add("Apple");  // 重复元素不会被添加

        System.out.println("HashSet内容：" + hashSet);
        System.out.println("元素个数：" + hashSet.size());

        // 检查元素
        System.out.println("是否包含Apple：" + hashSet.contains("Apple"));
        System.out.println("是否为空：" + hashSet.isEmpty());

        // 删除元素
        boolean removed = hashSet.remove("Banana");
        System.out.println("删除Banana成功：" + removed);
        System.out.println("删除后：" + hashSet);

        // 集合操作
        Set<String> otherSet = new HashSet<>();
        otherSet.add("Cherry");
        otherSet.add("Date");
        otherSet.add("Elderberry");

        // 并集
        Set<String> union = new HashSet<>(hashSet);
        union.addAll(otherSet);
        System.out.println("并集：" + union);

        // 交集
        Set<String> intersection = new HashSet<>(hashSet);
        intersection.retainAll(otherSet);
        System.out.println("交集：" + intersection);

        // 差集
        Set<String> difference = new HashSet<>(hashSet);
        difference.removeAll(otherSet);
        System.out.println("差集：" + difference);
    }
}
```

**TreeSet使用：**
```java
import java.util.*;

public class TreeSetDemo {
    public static void main(String[] args) {
        // TreeSet自动排序
        TreeSet<Integer> treeSet = new TreeSet<>();

        treeSet.add(5);
        treeSet.add(2);
        treeSet.add(8);
        treeSet.add(1);
        treeSet.add(9);

        System.out.println("TreeSet（自动排序）：" + treeSet);  // [1, 2, 5, 8, 9]

        // TreeSet特有方法
        System.out.println("第一个元素：" + treeSet.first());
        System.out.println("最后一个元素：" + treeSet.last());

        System.out.println("小于5的元素：" + treeSet.headSet(5));
        System.out.println("大于等于5的元素：" + treeSet.tailSet(5));
        System.out.println("2到8之间的元素：" + treeSet.subSet(2, 8));

        // 自定义排序
        TreeSet<String> stringTreeSet = new TreeSet<>(Comparator.reverseOrder());
        stringTreeSet.add("Banana");
        stringTreeSet.add("Apple");
        stringTreeSet.add("Cherry");

        System.out.println("逆序排列：" + stringTreeSet);  // [Cherry, Banana, Apple]

        // 自定义对象排序
        TreeSet<Person> personSet = new TreeSet<>((p1, p2) -> p1.getName().compareTo(p2.getName()));
        personSet.add(new Person("张三", 25));
        personSet.add(new Person("李四", 30));
        personSet.add(new Person("王五", 20));

        System.out.println("按姓名排序：");
        personSet.forEach(System.out::println);
    }
}

class Person {
    private String name;
    private int age;

    public Person(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public String getName() { return name; }
    public int getAge() { return age; }

    @Override
    public String toString() {
        return "Person{name='" + name + "', age=" + age + "}";
    }
}
```

### 📊 Day6核心知识点总结

#### 🎯 异常处理要点
1. **异常体系**：Throwable → Error/Exception → RuntimeException/CheckedException
2. **处理机制**：try-catch-finally，try-with-resources自动资源管理
3. **异常分类**：运行时异常（可选处理）、编译时异常（必须处理）
4. **自定义异常**：继承Exception或RuntimeException，提供有意义的错误信息
5. **最佳实践**：具体异常具体处理，避免捕获Exception，及时释放资源

#### 🎯 Collection集合要点
1. **集合体系**：Collection（List/Set）、Map，提供统一的数据结构操作
2. **List特点**：有序、可重复、支持索引访问
3. **Set特点**：无序、不可重复、基于哈希或树结构
4. **ArrayList vs LinkedList**：数组实现vs链表实现，随机访问vs插入删除
5. **HashSet vs TreeSet**：哈希表vs红黑树，无序vs有序

#### 🎯 集合操作要点
1. **增删改查**：add/remove/set/get等基本操作
2. **遍历方式**：for循环、增强for、Iterator、Lambda表达式
3. **集合运算**：并集（addAll）、交集（retainAll）、差集（removeAll）
4. **排序操作**：Collections.sort()、TreeSet自动排序、自定义Comparator
5. **性能考虑**：选择合适的集合类型，考虑时间复杂度

---

## 🗺️ Day7: Map集合与Stream API

### 📌 核心学习目标
- 掌握Map接口及其实现类的使用
- 理解HashMap、TreeMap、LinkedHashMap的区别
- 熟练运用Stream API进行数据处理
- 掌握Stream的中间操作和终端操作
- 理解并行流的概念和应用

### 🗂️ Map集合详解

#### 19.1 HashMap基础使用
**HashMap核心操作：**
```java
import java.util.*;

public class HashMapDemo {
    public static void main(String[] args) {
        Map<String, Integer> map = new HashMap<>();

        // 添加键值对
        map.put("Apple", 10);
        map.put("Banana", 20);
        map.put("Cherry", 15);
        map.put("Apple", 25);  // 覆盖原值

        System.out.println("Map内容：" + map);
        System.out.println("大小：" + map.size());

        // 获取值
        Integer appleCount = map.get("Apple");
        System.out.println("Apple数量：" + appleCount);

        // 安全获取（避免null）
        Integer orangeCount = map.getOrDefault("Orange", 0);
        System.out.println("Orange数量：" + orangeCount);

        // 检查键值
        System.out.println("是否包含Apple：" + map.containsKey("Apple"));
        System.out.println("是否包含值20：" + map.containsValue(20));

        // 删除操作
        Integer removed = map.remove("Banana");
        System.out.println("删除的值：" + removed);
        System.out.println("删除后：" + map);

        // 遍历方式
        System.out.println("\n遍历方式：");

        // 1. 遍历键
        for (String key : map.keySet()) {
            System.out.println("键：" + key + "，值：" + map.get(key));
        }

        // 2. 遍历值
        for (Integer value : map.values()) {
            System.out.println("值：" + value);
        }

        // 3. 遍历键值对
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            System.out.println("键值对：" + entry.getKey() + " = " + entry.getValue());
        }

        // 4. Lambda表达式遍历
        map.forEach((key, value) -> System.out.println("Lambda：" + key + " = " + value));
    }
}
```

#### 19.2 TreeMap和LinkedHashMap
**TreeMap自动排序：**
```java
import java.util.*;

public class TreeMapDemo {
    public static void main(String[] args) {
        // TreeMap按键自动排序
        TreeMap<String, Integer> treeMap = new TreeMap<>();

        treeMap.put("Zebra", 1);
        treeMap.put("Apple", 2);
        treeMap.put("Monkey", 3);
        treeMap.put("Banana", 4);

        System.out.println("TreeMap（按键排序）：" + treeMap);

        // TreeMap特有方法
        System.out.println("第一个键：" + treeMap.firstKey());
        System.out.println("最后一个键：" + treeMap.lastKey());

        System.out.println("小于M的键：" + treeMap.headMap("M"));
        System.out.println("大于等于M的键：" + treeMap.tailMap("M"));
        System.out.println("B到M之间的键：" + treeMap.subMap("B", "M"));

        // 自定义排序
        TreeMap<String, Integer> reverseMap = new TreeMap<>(Comparator.reverseOrder());
        reverseMap.putAll(treeMap);
        System.out.println("逆序TreeMap：" + reverseMap);
    }
}
```

**LinkedHashMap保持插入顺序：**
```java
import java.util.*;

public class LinkedHashMapDemo {
    public static void main(String[] args) {
        // LinkedHashMap保持插入顺序
        Map<String, Integer> linkedMap = new LinkedHashMap<>();

        linkedMap.put("Third", 3);
        linkedMap.put("First", 1);
        linkedMap.put("Second", 2);
        linkedMap.put("Fourth", 4);

        System.out.println("LinkedHashMap（插入顺序）：" + linkedMap);

        // 访问顺序的LinkedHashMap
        Map<String, Integer> accessOrderMap = new LinkedHashMap<>(16, 0.75f, true);
        accessOrderMap.put("A", 1);
        accessOrderMap.put("B", 2);
        accessOrderMap.put("C", 3);

        System.out.println("初始顺序：" + accessOrderMap);

        accessOrderMap.get("A");  // 访问A
        System.out.println("访问A后：" + accessOrderMap);  // A移到最后

        // 实现LRU缓存
        Map<String, String> lruCache = new LinkedHashMap<String, String>(3, 0.75f, true) {
            @Override
            protected boolean removeEldestEntry(Map.Entry<String, String> eldest) {
                return size() > 3;  // 超过3个元素时删除最老的
            }
        };

        lruCache.put("1", "One");
        lruCache.put("2", "Two");
        lruCache.put("3", "Three");
        System.out.println("LRU缓存：" + lruCache);

        lruCache.put("4", "Four");  // 会删除最老的元素
        System.out.println("添加第4个元素后：" + lruCache);
    }
}
```

### 🌊 Stream API详解

#### 20.1 Stream基础概念
**Stream创建方式：**
```java
import java.util.*;
import java.util.stream.*;

public class StreamBasicDemo {
    public static void main(String[] args) {
        // 1. 从集合创建Stream
        List<String> list = Arrays.asList("apple", "banana", "cherry", "date");
        Stream<String> stream1 = list.stream();

        // 2. 从数组创建Stream
        String[] array = {"red", "green", "blue"};
        Stream<String> stream2 = Arrays.stream(array);

        // 3. 使用Stream.of()
        Stream<Integer> stream3 = Stream.of(1, 2, 3, 4, 5);

        // 4. 无限流
        Stream<Integer> infiniteStream = Stream.iterate(0, n -> n + 2);
        Stream<Double> randomStream = Stream.generate(Math::random);

        // 5. 范围流
        IntStream range1 = IntStream.range(1, 10);        // 1到9
        IntStream range2 = IntStream.rangeClosed(1, 10);  // 1到10

        // 6. 空流
        Stream<String> emptyStream = Stream.empty();

        // 基础操作示例
        list.stream()
            .filter(s -> s.length() > 5)
            .map(String::toUpperCase)
            .forEach(System.out::println);
    }
}
```

#### 20.2 Stream中间操作
**过滤、映射、排序：**
```java
import java.util.*;
import java.util.stream.*;

public class StreamIntermediateDemo {
    public static void main(String[] args) {
        List<String> words = Arrays.asList("apple", "banana", "cherry", "date", "elderberry", "fig");

        // filter：过滤
        System.out.println("长度大于5的单词：");
        words.stream()
             .filter(word -> word.length() > 5)
             .forEach(System.out::println);

        // map：映射转换
        System.out.println("\n转换为大写：");
        words.stream()
             .map(String::toUpperCase)
             .forEach(System.out::println);

        // flatMap：扁平化映射
        List<List<String>> nestedList = Arrays.asList(
            Arrays.asList("a", "b"),
            Arrays.asList("c", "d"),
            Arrays.asList("e", "f")
        );

        System.out.println("\n扁平化处理：");
        nestedList.stream()
                  .flatMap(List::stream)
                  .forEach(System.out::println);

        // distinct：去重
        List<Integer> numbers = Arrays.asList(1, 2, 2, 3, 3, 3, 4, 5);
        System.out.println("\n去重后：");
        numbers.stream()
               .distinct()
               .forEach(System.out::println);

        // sorted：排序
        System.out.println("\n按长度排序：");
        words.stream()
             .sorted(Comparator.comparing(String::length))
             .forEach(System.out::println);

        // limit和skip：限制和跳过
        System.out.println("\n跳过前2个，取3个：");
        words.stream()
             .skip(2)
             .limit(3)
             .forEach(System.out::println);

        // peek：调试用，不改变流
        System.out.println("\n调试流处理过程：");
        words.stream()
             .filter(word -> word.length() > 4)
             .peek(word -> System.out.println("过滤后：" + word))
             .map(String::toUpperCase)
             .peek(word -> System.out.println("转换后：" + word))
             .collect(Collectors.toList());
    }
}
```

#### 20.3 Stream终端操作
**收集、聚合、查找：**
```java
import java.util.*;
import java.util.stream.*;

public class StreamTerminalDemo {
    public static void main(String[] args) {
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);

        // collect：收集到集合
        List<Integer> evenNumbers = numbers.stream()
                                          .filter(n -> n % 2 == 0)
                                          .collect(Collectors.toList());
        System.out.println("偶数：" + evenNumbers);

        Set<Integer> oddNumbers = numbers.stream()
                                        .filter(n -> n % 2 == 1)
                                        .collect(Collectors.toSet());
        System.out.println("奇数：" + oddNumbers);

        // 收集到Map
        List<String> words = Arrays.asList("apple", "banana", "cherry");
        Map<String, Integer> wordLengths = words.stream()
                                               .collect(Collectors.toMap(
                                                   word -> word,
                                                   String::length
                                               ));
        System.out.println("单词长度映射：" + wordLengths);

        // reduce：归约操作
        Optional<Integer> sum = numbers.stream()
                                      .reduce((a, b) -> a + b);
        System.out.println("求和：" + sum.orElse(0));

        Optional<Integer> max = numbers.stream()
                                      .reduce(Integer::max);
        System.out.println("最大值：" + max.orElse(0));

        // 带初始值的reduce
        Integer product = numbers.stream()
                                .reduce(1, (a, b) -> a * b);
        System.out.println("乘积：" + product);

        // count：计数
        long count = numbers.stream()
                           .filter(n -> n > 5)
                           .count();
        System.out.println("大于5的数字个数：" + count);

        // anyMatch、allMatch、noneMatch：匹配
        boolean hasEven = numbers.stream().anyMatch(n -> n % 2 == 0);
        boolean allPositive = numbers.stream().allMatch(n -> n > 0);
        boolean noneNegative = numbers.stream().noneMatch(n -> n < 0);

        System.out.println("是否有偶数：" + hasEven);
        System.out.println("是否都是正数：" + allPositive);
        System.out.println("是否没有负数：" + noneNegative);

        // findFirst、findAny：查找
        Optional<Integer> first = numbers.stream()
                                        .filter(n -> n > 5)
                                        .findFirst();
        System.out.println("第一个大于5的数：" + first.orElse(-1));

        // forEach：遍历
        System.out.println("所有数字：");
        numbers.stream().forEach(System.out::print);
        System.out.println();
    }
}
```

#### 20.4 并行流 (Parallel Streams)
**并行处理：**
```java
import java.util.*;
import java.util.stream.*;

public class ParallelStreamDemo {
    public static void main(String[] args) {
        List<Integer> numbers = IntStream.rangeClosed(1, 1000000)
                                        .boxed()
                                        .collect(Collectors.toList());

        // 串行流处理
        long start1 = System.currentTimeMillis();
        long sum1 = numbers.stream()
                          .mapToLong(Integer::longValue)
                          .sum();
        long end1 = System.currentTimeMillis();
        System.out.println("串行流求和：" + sum1 + "，耗时：" + (end1 - start1) + "ms");

        // 并行流处理
        long start2 = System.currentTimeMillis();
        long sum2 = numbers.parallelStream()
                          .mapToLong(Integer::longValue)
                          .sum();
        long end2 = System.currentTimeMillis();
        System.out.println("并行流求和：" + sum2 + "，耗时：" + (end2 - start2) + "ms");

        // 并行流注意事项
        List<String> words = Arrays.asList("apple", "banana", "cherry", "date");

        // 线程安全的收集器
        List<String> result = words.parallelStream()
                                  .map(String::toUpperCase)
                                  .collect(Collectors.toList());
        System.out.println("并行处理结果：" + result);

        // 检查是否为并行流
        boolean isParallel = words.parallelStream().isParallel();
        System.out.println("是否为并行流：" + isParallel);

        // 转换为串行流
        List<String> serialResult = words.parallelStream()
                                        .sequential()
                                        .map(String::toLowerCase)
                                        .collect(Collectors.toList());
        System.out.println("转为串行流处理：" + serialResult);
    }
}
```

### 📊 Day7核心知识点总结

#### 🎯 Map集合要点
1. **Map特点**：键值对存储，键唯一，值可重复
2. **HashMap**：基于哈希表，无序，允许null键值，线程不安全
3. **TreeMap**：基于红黑树，按键排序，不允许null键，线程不安全
4. **LinkedHashMap**：保持插入顺序或访问顺序，可实现LRU缓存
5. **遍历方式**：keySet()、values()、entrySet()、forEach()

#### 🎯 Stream API要点
1. **流特性**：不存储数据、函数式编程、延迟执行、可能无限
2. **中间操作**：filter、map、flatMap、distinct、sorted、limit、skip
3. **终端操作**：collect、reduce、count、forEach、anyMatch、findFirst
4. **并行流**：parallelStream()提高处理性能，注意线程安全
5. **最佳实践**：链式调用、避免副作用、合理使用并行流

#### 🎯 数据处理要点
1. **函数式编程**：声明式编程风格，关注做什么而不是怎么做
2. **链式操作**：多个操作可以链式组合，提高代码可读性
3. **延迟执行**：中间操作不会立即执行，直到遇到终端操作
4. **性能优化**：合理选择集合类型和流操作，避免不必要的装箱拆箱
5. **代码简洁**：使用Stream API可以大大简化集合操作代码

---

## 📁 Day8: IO流与文件操作

### 📌 核心学习目标
- 理解IO流的分类和体系结构
- 掌握字节流和字符流的使用方法
- 熟练运用缓冲流提高IO性能
- 掌握对象序列化和反序列化
- 理解NIO的基本概念和应用

### 🌊 IO流体系概述

#### 21.1 IO流分类
**IO流体系结构：**
```
IO流
├── 字节流（Byte Streams）
│   ├── InputStream（输入字节流）
│   │   ├── FileInputStream
│   │   ├── BufferedInputStream
│   │   └── ObjectInputStream
│   └── OutputStream（输出字节流）
│       ├── FileOutputStream
│       ├── BufferedOutputStream
│       └── ObjectOutputStream
└── 字符流（Character Streams）
    ├── Reader（输入字符流）
    │   ├── FileReader
    │   ├── BufferedReader
    │   └── InputStreamReader
    └── Writer（输出字符流）
        ├── FileWriter
        ├── BufferedWriter
        └── OutputStreamWriter
```

#### 21.2 字节流基础操作
**FileInputStream和FileOutputStream：**
```java
import java.io.*;

public class ByteStreamDemo {
    public static void main(String[] args) {
        // 写入文件
        writeFile();

        // 读取文件
        readFile();

        // 复制文件
        copyFile();
    }

    public static void writeFile() {
        try (FileOutputStream fos = new FileOutputStream("test.txt")) {
            String content = "Hello, Java IO!";
            fos.write(content.getBytes());
            System.out.println("文件写入成功");
        } catch (IOException e) {
            System.out.println("写入文件失败：" + e.getMessage());
        }
    }

    public static void readFile() {
        try (FileInputStream fis = new FileInputStream("test.txt")) {
            byte[] buffer = new byte[1024];
            int bytesRead = fis.read(buffer);

            if (bytesRead != -1) {
                String content = new String(buffer, 0, bytesRead);
                System.out.println("读取内容：" + content);
            }
        } catch (IOException e) {
            System.out.println("读取文件失败：" + e.getMessage());
        }
    }

    public static void copyFile() {
        try (FileInputStream fis = new FileInputStream("test.txt");
             FileOutputStream fos = new FileOutputStream("copy.txt")) {

            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }

            System.out.println("文件复制成功");
        } catch (IOException e) {
            System.out.println("文件复制失败：" + e.getMessage());
        }
    }
}
```

#### 21.3 字符流基础操作
**FileReader和FileWriter：**
```java
import java.io.*;

public class CharacterStreamDemo {
    public static void main(String[] args) {
        // 写入文本文件
        writeTextFile();

        // 读取文本文件
        readTextFile();

        // 追加内容
        appendToFile();
    }

    public static void writeTextFile() {
        try (FileWriter writer = new FileWriter("text.txt")) {
            writer.write("第一行内容\n");
            writer.write("第二行内容\n");
            writer.write("第三行内容\n");
            System.out.println("文本写入成功");
        } catch (IOException e) {
            System.out.println("写入失败：" + e.getMessage());
        }
    }

    public static void readTextFile() {
        try (FileReader reader = new FileReader("text.txt")) {
            char[] buffer = new char[1024];
            int charsRead = reader.read(buffer);

            if (charsRead != -1) {
                String content = new String(buffer, 0, charsRead);
                System.out.println("读取内容：\n" + content);
            }
        } catch (IOException e) {
            System.out.println("读取失败：" + e.getMessage());
        }
    }

    public static void appendToFile() {
        try (FileWriter writer = new FileWriter("text.txt", true)) {  // true表示追加模式
            writer.write("追加的内容\n");
            System.out.println("内容追加成功");
        } catch (IOException e) {
            System.out.println("追加失败：" + e.getMessage());
        }
    }
}
```

#### 21.4 缓冲流性能优化
**BufferedInputStream和BufferedOutputStream：**
```java
import java.io.*;

public class BufferedStreamDemo {
    public static void main(String[] args) {
        // 创建测试文件
        createLargeFile();

        // 比较性能
        comparePerformance();

        // 缓冲字符流
        bufferedCharacterStream();
    }

    public static void createLargeFile() {
        try (FileOutputStream fos = new FileOutputStream("large.txt")) {
            for (int i = 0; i < 100000; i++) {
                fos.write(("Line " + i + "\n").getBytes());
            }
            System.out.println("大文件创建完成");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void comparePerformance() {
        // 普通字节流复制
        long start1 = System.currentTimeMillis();
        copyWithoutBuffer();
        long end1 = System.currentTimeMillis();
        System.out.println("普通流复制耗时：" + (end1 - start1) + "ms");

        // 缓冲流复制
        long start2 = System.currentTimeMillis();
        copyWithBuffer();
        long end2 = System.currentTimeMillis();
        System.out.println("缓冲流复制耗时：" + (end2 - start2) + "ms");
    }

    public static void copyWithoutBuffer() {
        try (FileInputStream fis = new FileInputStream("large.txt");
             FileOutputStream fos = new FileOutputStream("copy1.txt")) {

            int data;
            while ((data = fis.read()) != -1) {
                fos.write(data);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void copyWithBuffer() {
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream("large.txt"));
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream("copy2.txt"))) {

            int data;
            while ((data = bis.read()) != -1) {
                bos.write(data);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void bufferedCharacterStream() {
        try (BufferedReader reader = new BufferedReader(new FileReader("text.txt"));
             BufferedWriter writer = new BufferedWriter(new FileWriter("buffered_output.txt"))) {

            String line;
            int lineNumber = 1;

            while ((line = reader.readLine()) != null) {
                writer.write(lineNumber + ": " + line);
                writer.newLine();
                lineNumber++;
            }

            System.out.println("缓冲字符流处理完成");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
```

#### 21.5 对象序列化
**Serializable接口：**
```java
import java.io.*;
import java.util.*;

// 可序列化的学生类
class Student implements Serializable {
    private static final long serialVersionUID = 1L;

    private String name;
    private int age;
    private transient String password;  // transient字段不会被序列化
    private static String school = "Java学院";  // static字段不会被序列化

    public Student(String name, int age, String password) {
        this.name = name;
        this.age = age;
        this.password = password;
    }

    // getter和setter方法
    public String getName() { return name; }
    public int getAge() { return age; }
    public String getPassword() { return password; }

    @Override
    public String toString() {
        return "Student{name='" + name + "', age=" + age +
               ", password='" + password + "', school='" + school + "'}";
    }
}

public class SerializationDemo {
    public static void main(String[] args) {
        // 序列化对象
        serializeObject();

        // 反序列化对象
        deserializeObject();

        // 序列化集合
        serializeCollection();

        // 反序列化集合
        deserializeCollection();
    }

    public static void serializeObject() {
        Student student = new Student("张三", 20, "secret123");

        try (ObjectOutputStream oos = new ObjectOutputStream(
                new FileOutputStream("student.ser"))) {

            oos.writeObject(student);
            System.out.println("对象序列化成功：" + student);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void deserializeObject() {
        try (ObjectInputStream ois = new ObjectInputStream(
                new FileInputStream("student.ser"))) {

            Student student = (Student) ois.readObject();
            System.out.println("对象反序列化成功：" + student);

        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }
    }

    public static void serializeCollection() {
        List<Student> students = Arrays.asList(
            new Student("李四", 21, "pass456"),
            new Student("王五", 22, "pwd789"),
            new Student("赵六", 23, "secret000")
        );

        try (ObjectOutputStream oos = new ObjectOutputStream(
                new FileOutputStream("students.ser"))) {

            oos.writeObject(students);
            System.out.println("集合序列化成功，包含 " + students.size() + " 个学生");

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @SuppressWarnings("unchecked")
    public static void deserializeCollection() {
        try (ObjectInputStream ois = new ObjectInputStream(
                new FileInputStream("students.ser"))) {

            List<Student> students = (List<Student>) ois.readObject();
            System.out.println("集合反序列化成功：");
            students.forEach(System.out::println);

        } catch (IOException | ClassNotFoundException e) {
            e.printStackTrace();
        }
    }
}
```

### 📊 Day8核心知识点总结

#### 🎯 IO流要点
1. **流分类**：字节流（处理二进制数据）、字符流（处理文本数据）
2. **流方向**：输入流（读取数据）、输出流（写入数据）
3. **缓冲流**：提高IO性能，减少系统调用次数
4. **资源管理**：使用try-with-resources自动关闭流
5. **异常处理**：IO操作必须处理IOException

#### 🎯 文件操作要点
1. **字节流**：适合处理图片、音频、视频等二进制文件
2. **字符流**：适合处理文本文件，自动处理字符编码
3. **缓冲区**：BufferedInputStream/OutputStream显著提高性能
4. **文件模式**：读取模式、写入模式、追加模式
5. **路径处理**：相对路径、绝对路径、路径分隔符

#### 🎯 序列化要点
1. **Serializable接口**：标记接口，表示类可以被序列化
2. **serialVersionUID**：版本控制，确保序列化兼容性
3. **transient关键字**：标记不需要序列化的字段
4. **静态字段**：不会被序列化，因为属于类而不是对象
5. **应用场景**：对象持久化、网络传输、缓存存储

---

## 🧵 Day9: 多线程编程基础

### 📌 核心学习目标
- 理解线程的概念和生命周期
- 掌握创建线程的多种方式
- 熟练运用线程同步机制
- 理解线程安全问题和解决方案
- 掌握线程池的使用方法

### 🔄 线程基础概念

#### 22.1 线程生命周期
**线程状态转换：**
```
NEW（新建）
    ↓ start()
RUNNABLE（可运行）
    ↓ 获得CPU时间片
RUNNING（运行中）
    ↓ sleep()/wait()/阻塞IO
BLOCKED/WAITING/TIMED_WAITING（阻塞/等待）
    ↓ 条件满足
RUNNABLE（可运行）
    ↓ 线程执行完毕
TERMINATED（终止）
```

#### 22.2 创建线程的方式
**方式一：继承Thread类**
```java
class MyThread extends Thread {
    private String threadName;

    public MyThread(String name) {
        this.threadName = name;
    }

    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            System.out.println(threadName + " - 计数: " + i);
            try {
                Thread.sleep(1000);  // 休眠1秒
            } catch (InterruptedException e) {
                System.out.println(threadName + " 被中断");
                return;
            }
        }
        System.out.println(threadName + " 执行完毕");
    }
}

public class ThreadDemo1 {
    public static void main(String[] args) {
        // 创建线程对象
        MyThread thread1 = new MyThread("线程1");
        MyThread thread2 = new MyThread("线程2");

        // 启动线程
        thread1.start();
        thread2.start();

        System.out.println("主线程继续执行");

        // 等待线程结束
        try {
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("所有线程执行完毕");
    }
}
```

**方式二：实现Runnable接口**
```java
class MyRunnable implements Runnable {
    private String taskName;

    public MyRunnable(String name) {
        this.taskName = name;
    }

    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            System.out.println(taskName + " - 执行第 " + i + " 次");
            try {
                Thread.sleep(800);
            } catch (InterruptedException e) {
                System.out.println(taskName + " 被中断");
                return;
            }
        }
        System.out.println(taskName + " 任务完成");
    }
}

public class ThreadDemo2 {
    public static void main(String[] args) {
        // 创建Runnable对象
        MyRunnable task1 = new MyRunnable("任务A");
        MyRunnable task2 = new MyRunnable("任务B");

        // 创建Thread对象
        Thread thread1 = new Thread(task1);
        Thread thread2 = new Thread(task2);

        // 设置线程名称
        thread1.setName("工作线程1");
        thread2.setName("工作线程2");

        // 启动线程
        thread1.start();
        thread2.start();

        // 使用Lambda表达式创建线程
        Thread lambdaThread = new Thread(() -> {
            for (int i = 1; i <= 3; i++) {
                System.out.println("Lambda线程 - " + i);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        });

        lambdaThread.start();
    }
}
```

**方式三：实现Callable接口**
```java
import java.util.concurrent.*;

class MyCallable implements Callable<String> {
    private String taskName;

    public MyCallable(String name) {
        this.taskName = name;
    }

    @Override
    public String call() throws Exception {
        int sum = 0;
        for (int i = 1; i <= 100; i++) {
            sum += i;
            Thread.sleep(10);  // 模拟耗时操作
        }
        return taskName + " 计算结果: " + sum;
    }
}

public class CallableDemo {
    public static void main(String[] args) {
        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(2);

        // 创建Callable任务
        MyCallable task1 = new MyCallable("任务1");
        MyCallable task2 = new MyCallable("任务2");

        try {
            // 提交任务并获取Future对象
            Future<String> future1 = executor.submit(task1);
            Future<String> future2 = executor.submit(task2);

            // 获取执行结果
            String result1 = future1.get();  // 阻塞等待结果
            String result2 = future2.get();

            System.out.println(result1);
            System.out.println(result2);

            // 检查任务状态
            System.out.println("任务1是否完成: " + future1.isDone());
            System.out.println("任务2是否取消: " + future2.isCancelled());

        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
    }
}
```

### 🔒 线程同步机制

#### 23.1 synchronized关键字
**同步方法和同步代码块：**
```java
class Counter {
    private int count = 0;

    // 同步方法
    public synchronized void increment() {
        count++;
    }

    // 同步代码块
    public void decrement() {
        synchronized (this) {
            count--;
        }
    }

    public synchronized int getCount() {
        return count;
    }
}

class CounterTask implements Runnable {
    private Counter counter;
    private boolean isIncrement;

    public CounterTask(Counter counter, boolean isIncrement) {
        this.counter = counter;
        this.isIncrement = isIncrement;
    }

    @Override
    public void run() {
        for (int i = 0; i < 1000; i++) {
            if (isIncrement) {
                counter.increment();
            } else {
                counter.decrement();
            }
        }
    }
}

public class SynchronizedDemo {
    public static void main(String[] args) {
        Counter counter = new Counter();

        // 创建线程
        Thread t1 = new Thread(new CounterTask(counter, true));
        Thread t2 = new Thread(new CounterTask(counter, false));
        Thread t3 = new Thread(new CounterTask(counter, true));

        // 启动线程
        t1.start();
        t2.start();
        t3.start();

        // 等待所有线程完成
        try {
            t1.join();
            t2.join();
            t3.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("最终计数值: " + counter.getCount());
    }
}
```

#### 23.2 Lock接口
**ReentrantLock使用：**
```java
import java.util.concurrent.locks.*;

class BankAccount {
    private double balance;
    private final ReentrantLock lock = new ReentrantLock();

    public BankAccount(double initialBalance) {
        this.balance = initialBalance;
    }

    public void deposit(double amount) {
        lock.lock();
        try {
            System.out.println(Thread.currentThread().getName() + " 存款前余额: " + balance);
            balance += amount;
            System.out.println(Thread.currentThread().getName() + " 存款 " + amount + ", 余额: " + balance);
        } finally {
            lock.unlock();
        }
    }

    public boolean withdraw(double amount) {
        lock.lock();
        try {
            System.out.println(Thread.currentThread().getName() + " 取款前余额: " + balance);
            if (balance >= amount) {
                balance -= amount;
                System.out.println(Thread.currentThread().getName() + " 取款 " + amount + ", 余额: " + balance);
                return true;
            } else {
                System.out.println(Thread.currentThread().getName() + " 余额不足，取款失败");
                return false;
            }
        } finally {
            lock.unlock();
        }
    }

    public double getBalance() {
        lock.lock();
        try {
            return balance;
        } finally {
            lock.unlock();
        }
    }
}

public class LockDemo {
    public static void main(String[] args) {
        BankAccount account = new BankAccount(1000);

        // 创建多个线程进行存取款操作
        Thread t1 = new Thread(() -> {
            for (int i = 0; i < 3; i++) {
                account.deposit(100);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }, "存款线程");

        Thread t2 = new Thread(() -> {
            for (int i = 0; i < 3; i++) {
                account.withdraw(150);
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }, "取款线程");

        t1.start();
        t2.start();

        try {
            t1.join();
            t2.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("最终余额: " + account.getBalance());
    }
}
```

#### 23.3 线程通信
**wait()和notify()机制：**
```java
class SharedResource {
    private int data;
    private boolean hasData = false;

    public synchronized void produce(int value) {
        while (hasData) {
            try {
                wait();  // 等待消费者消费
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        data = value;
        hasData = true;
        System.out.println("生产者生产了: " + value);
        notify();  // 通知消费者
    }

    public synchronized int consume() {
        while (!hasData) {
            try {
                wait();  // 等待生产者生产
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        hasData = false;
        System.out.println("消费者消费了: " + data);
        notify();  // 通知生产者
        return data;
    }
}

class Producer implements Runnable {
    private SharedResource resource;

    public Producer(SharedResource resource) {
        this.resource = resource;
    }

    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            resource.produce(i);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}

class Consumer implements Runnable {
    private SharedResource resource;

    public Consumer(SharedResource resource) {
        this.resource = resource;
    }

    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            int value = resource.consume();
            try {
                Thread.sleep(1500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}

public class ProducerConsumerDemo {
    public static void main(String[] args) {
        SharedResource resource = new SharedResource();

        Thread producer = new Thread(new Producer(resource), "生产者");
        Thread consumer = new Thread(new Consumer(resource), "消费者");

        producer.start();
        consumer.start();

        try {
            producer.join();
            consumer.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("生产消费完成");
    }
}
```

### 🏊‍♂️ 线程池

#### 24.1 线程池基础
**Executors工厂类：**
```java
import java.util.concurrent.*;

public class ThreadPoolDemo {
    public static void main(String[] args) {
        // 固定大小线程池
        ExecutorService fixedPool = Executors.newFixedThreadPool(3);

        // 缓存线程池
        ExecutorService cachedPool = Executors.newCachedThreadPool();

        // 单线程池
        ExecutorService singlePool = Executors.newSingleThreadExecutor();

        // 定时任务线程池
        ScheduledExecutorService scheduledPool = Executors.newScheduledThreadPool(2);

        // 使用固定线程池执行任务
        for (int i = 1; i <= 10; i++) {
            final int taskId = i;
            fixedPool.submit(() -> {
                System.out.println("任务 " + taskId + " 由 " + Thread.currentThread().getName() + " 执行");
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                System.out.println("任务 " + taskId + " 完成");
            });
        }

        // 定时任务
        scheduledPool.scheduleAtFixedRate(() -> {
            System.out.println("定时任务执行: " + System.currentTimeMillis());
        }, 1, 3, TimeUnit.SECONDS);  // 1秒后开始，每3秒执行一次

        // 关闭线程池
        fixedPool.shutdown();
        cachedPool.shutdown();
        singlePool.shutdown();

        // 等待5秒后关闭定时任务
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        scheduledPool.shutdown();
    }
}
```

### 📊 Day9核心知识点总结

#### 🎯 多线程要点
1. **线程创建**：继承Thread、实现Runnable、实现Callable
2. **线程生命周期**：NEW → RUNNABLE → RUNNING → BLOCKED/WAITING → TERMINATED
3. **线程同步**：synchronized关键字、Lock接口、volatile关键字
4. **线程通信**：wait()/notify()、Condition接口
5. **线程池**：复用线程、控制并发数、提高性能

#### 🎯 线程安全要点
1. **竞态条件**：多线程访问共享资源时可能出现的问题
2. **同步机制**：确保同一时间只有一个线程访问临界资源
3. **死锁预防**：避免循环等待、合理设计锁的获取顺序
4. **性能考虑**：同步会影响性能，需要平衡安全性和效率
5. **最佳实践**：尽量使用线程安全的类、减少锁的粒度

---

## 🌐 Day10: 网络编程基础

### 📌 核心学习目标
- 理解网络编程的基本概念
- 掌握TCP和UDP协议的使用
- 熟练运用Socket编程
- 了解HTTP客户端编程
- 掌握多线程服务器的实现

### 🔌 Socket编程基础

#### 25.1 TCP Socket通信
**TCP服务器端：**
```java
import java.io.*;
import java.net.*;

public class TCPServer {
    public static void main(String[] args) {
        try (ServerSocket serverSocket = new ServerSocket(8888)) {
            System.out.println("服务器启动，监听端口 8888");

            while (true) {
                // 等待客户端连接
                Socket clientSocket = serverSocket.accept();
                System.out.println("客户端连接：" + clientSocket.getInetAddress());

                // 为每个客户端创建新线程
                new Thread(new ClientHandler(clientSocket)).start();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

class ClientHandler implements Runnable {
    private Socket clientSocket;

    public ClientHandler(Socket socket) {
        this.clientSocket = socket;
    }

    @Override
    public void run() {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter writer = new PrintWriter(
                clientSocket.getOutputStream(), true)) {

            String inputLine;
            while ((inputLine = reader.readLine()) != null) {
                System.out.println("收到消息：" + inputLine);

                if ("bye".equalsIgnoreCase(inputLine)) {
                    writer.println("再见！");
                    break;
                }

                // 回显消息
                writer.println("服务器回复：" + inputLine);
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                clientSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
```

**TCP客户端：**
```java
import java.io.*;
import java.net.*;
import java.util.Scanner;

public class TCPClient {
    public static void main(String[] args) {
        try (Socket socket = new Socket("localhost", 8888);
             BufferedReader reader = new BufferedReader(
                new InputStreamReader(socket.getInputStream()));
             PrintWriter writer = new PrintWriter(
                socket.getOutputStream(), true);
             Scanner scanner = new Scanner(System.in)) {

            System.out.println("连接到服务器成功！");
            System.out.println("输入消息（输入 'bye' 退出）：");

            String userInput;
            while ((userInput = scanner.nextLine()) != null) {
                // 发送消息到服务器
                writer.println(userInput);

                // 接收服务器回复
                String response = reader.readLine();
                System.out.println(response);

                if ("bye".equalsIgnoreCase(userInput)) {
                    break;
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
```

#### 25.2 UDP Socket通信
**UDP服务器端：**
```java
import java.net.*;
import java.io.*;

public class UDPServer {
    public static void main(String[] args) {
        try (DatagramSocket socket = new DatagramSocket(9999)) {
            System.out.println("UDP服务器启动，监听端口 9999");

            byte[] buffer = new byte[1024];

            while (true) {
                // 接收数据包
                DatagramPacket receivePacket = new DatagramPacket(buffer, buffer.length);
                socket.receive(receivePacket);

                String message = new String(receivePacket.getData(), 0, receivePacket.getLength());
                System.out.println("收到消息：" + message + " 来自：" + receivePacket.getAddress());

                // 回复消息
                String response = "服务器收到：" + message;
                byte[] responseData = response.getBytes();

                DatagramPacket sendPacket = new DatagramPacket(
                    responseData, responseData.length,
                    receivePacket.getAddress(), receivePacket.getPort()
                );

                socket.send(sendPacket);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
```

**UDP客户端：**
```java
import java.net.*;
import java.io.*;
import java.util.Scanner;

public class UDPClient {
    public static void main(String[] args) {
        try (DatagramSocket socket = new DatagramSocket();
             Scanner scanner = new Scanner(System.in)) {

            InetAddress serverAddress = InetAddress.getByName("localhost");
            int serverPort = 9999;

            System.out.println("UDP客户端启动，输入消息：");

            String userInput;
            while ((userInput = scanner.nextLine()) != null) {
                if ("quit".equalsIgnoreCase(userInput)) {
                    break;
                }

                // 发送数据包
                byte[] sendData = userInput.getBytes();
                DatagramPacket sendPacket = new DatagramPacket(
                    sendData, sendData.length, serverAddress, serverPort
                );
                socket.send(sendPacket);

                // 接收回复
                byte[] receiveBuffer = new byte[1024];
                DatagramPacket receivePacket = new DatagramPacket(
                    receiveBuffer, receiveBuffer.length
                );
                socket.receive(receivePacket);

                String response = new String(receivePacket.getData(), 0, receivePacket.getLength());
                System.out.println("服务器回复：" + response);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
```

### 📊 Day10核心知识点总结

#### 🎯 网络编程要点
1. **协议选择**：TCP（可靠连接）vs UDP（无连接快速）
2. **Socket编程**：ServerSocket/Socket（TCP）、DatagramSocket（UDP）
3. **多线程服务器**：为每个客户端创建独立线程处理
4. **异常处理**：网络编程必须处理各种IO异常
5. **资源管理**：及时关闭Socket和流资源

---

## 📄 Day11: 特殊文件处理与日志

### 📌 核心学习目标
- 掌握Properties文件的读写操作
- 熟练运用XML文件的解析和生成
- 理解日志框架的使用方法
- 掌握配置文件的管理技巧
- 了解JSON数据的处理方式

### ⚙️ Properties文件处理

#### 26.1 Properties基础操作
**读写Properties文件：**
```java
import java.io.*;
import java.util.Properties;

public class PropertiesDemo {
    public static void main(String[] args) {
        // 写入Properties文件
        writeProperties();

        // 读取Properties文件
        readProperties();

        // 系统属性
        systemProperties();
    }

    public static void writeProperties() {
        Properties props = new Properties();

        // 设置属性
        props.setProperty("database.url", "********************************");
        props.setProperty("database.username", "root");
        props.setProperty("database.password", "123456");
        props.setProperty("database.driver", "com.mysql.cj.jdbc.Driver");
        props.setProperty("app.version", "1.0.0");
        props.setProperty("app.name", "Java学习系统");

        try (FileOutputStream fos = new FileOutputStream("config.properties")) {
            props.store(fos, "数据库配置文件");
            System.out.println("Properties文件写入成功");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void readProperties() {
        Properties props = new Properties();

        try (FileInputStream fis = new FileInputStream("config.properties")) {
            props.load(fis);

            // 读取属性
            String url = props.getProperty("database.url");
            String username = props.getProperty("database.username");
            String password = props.getProperty("database.password");
            String version = props.getProperty("app.version", "未知版本");  // 默认值

            System.out.println("数据库URL：" + url);
            System.out.println("用户名：" + username);
            System.out.println("密码：" + password);
            System.out.println("应用版本：" + version);

            // 遍历所有属性
            System.out.println("\n所有配置项：");
            props.forEach((key, value) ->
                System.out.println(key + " = " + value));

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void systemProperties() {
        Properties sysProps = System.getProperties();

        System.out.println("\n系统属性：");
        System.out.println("Java版本：" + sysProps.getProperty("java.version"));
        System.out.println("操作系统：" + sysProps.getProperty("os.name"));
        System.out.println("用户目录：" + sysProps.getProperty("user.home"));
        System.out.println("文件分隔符：" + sysProps.getProperty("file.separator"));
        System.out.println("路径分隔符：" + sysProps.getProperty("path.separator"));
    }
}
```

### 📊 完整学习总结

#### 🎯 Day11核心知识点总结
1. **Properties文件**：键值对配置文件，常用于应用配置
2. **XML处理**：结构化数据存储，支持复杂的数据关系
3. **日志系统**：记录应用运行状态，便于调试和监控
4. **配置管理**：集中管理应用配置，提高可维护性
5. **文件格式**：选择合适的文件格式存储不同类型的数据

---

## 🎓 Java学习总结与展望

### 📈 学习成果回顾

通过Day1到Day11的系统学习，我们完成了Java核心技术的全面掌握：

#### 🏗️ 面向对象编程基础
- **静态特性**：深入理解static关键字的使用场景和内存模型
- **继承机制**：掌握类的继承关系和方法重写技巧
- **多态性**：理解动态绑定和类型转换的原理
- **抽象与接口**：学会使用抽象类和接口设计灵活的程序结构

#### 🔧 高级语言特性
- **内部类**：掌握四种内部类的特点和应用场景
- **泛型编程**：实现类型安全的通用代码设计
- **枚举类型**：使用枚举提高代码的可读性和安全性
- **Lambda表达式**：函数式编程思想的实践应用

#### 📚 核心API与工具
- **字符串处理**：高效的字符串操作和性能优化
- **集合框架**：List、Set、Map的选择和使用技巧
- **Stream API**：声明式数据处理和函数式编程
- **时间API**：现代化的日期时间处理方案

#### 🔄 系统编程技能
- **异常处理**：健壮的错误处理机制设计
- **IO操作**：文件读写和数据序列化技术
- **多线程**：并发编程和线程安全保障
- **网络编程**：Socket通信和分布式应用基础

### 🚀 技能提升建议

#### 📖 深入学习方向
1. **设计模式**：学习常用设计模式，提高代码设计能力
2. **JVM原理**：理解Java虚拟机工作机制，优化程序性能
3. **并发编程**：深入学习java.util.concurrent包的高级特性
4. **框架技术**：Spring、MyBatis等企业级开发框架

#### 🛠️ 实践项目建议
1. **控制台应用**：学生管理系统、图书管理系统
2. **网络应用**：聊天室、文件传输工具
3. **数据处理**：日志分析工具、数据统计系统
4. **Web应用**：结合Servlet/JSP开发Web项目

#### 📝 编程习惯培养
1. **代码规范**：遵循Java编码规范，提高代码可读性
2. **单元测试**：使用JUnit编写测试用例，保证代码质量
3. **版本控制**：使用Git管理代码版本，培养团队协作能力
4. **文档编写**：养成编写技术文档的好习惯

### 🎯 学习成效评估

#### ✅ 已掌握的核心技能
- [x] 面向对象编程思想和设计原则
- [x] Java语法特性和高级特性应用
- [x] 集合框架和Stream API的熟练使用
- [x] 异常处理和IO操作的最佳实践
- [x] 多线程编程和并发控制机制
- [x] 网络编程和Socket通信技术

#### 🔄 持续改进方向
- 加强算法和数据结构的学习
- 深入理解JVM内存管理和性能调优
- 学习企业级开发框架和工具
- 培养系统设计和架构思维
- 提高代码质量和工程化能力

### 💡 学习心得与建议

1. **理论与实践结合**：每个知识点都要通过代码实践来加深理解
2. **循序渐进**：从基础概念开始，逐步深入到高级特性
3. **多做项目**：通过实际项目来综合运用所学知识
4. **持续学习**：Java技术不断发展，要保持学习的热情
5. **交流分享**：与其他开发者交流经验，共同进步

---

## 📚 参考资料与扩展阅读

### 📖 推荐书籍
- 《Java核心技术》- Cay S. Horstmann
- 《Effective Java》- Joshua Bloch
- 《Java并发编程实战》- Brian Goetz
- 《深入理解Java虚拟机》- 周志明

### 🌐 在线资源
- Oracle官方Java文档
- Java SE API文档
- GitHub开源项目
- Stack Overflow技术问答

### 🎓 认证考试
- Oracle Certified Associate (OCA)
- Oracle Certified Professional (OCP)
- Oracle Certified Master (OCM)

---

**🎉 恭喜完成Java核心技术的系统学习！继续保持学习的热情，在编程的道路上不断前进！**
