package d5_regex;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * @Description: 高级正则表达式规则和技巧
 * @Author: Alhz
 * @Date: 2025/7/26 - 15:27
 **/
public class AdvancedRegexRules {
    public static void main(String[] args) {
        System.out.println("=============== 高级正则表达式规则 ===============\n");
        
        demonstrateAdvancedFeatures();
        demonstrateCommonPatterns();
        demonstratePerformanceTips();
    }
    
    private static void demonstrateAdvancedFeatures() {
        System.out.println("1. 高级特性:");
        
        // 前瞻断言 (?=...)
        System.out.println("正向前瞻断言 (?=...):");
        testRegex("\\d+(?=元)", "100元", "200", "300元人民币");
        
        // 负向前瞻断言 (?!...)
        System.out.println("负向前瞻断言 (?!...):");
        testRegex("\\d+(?!元)", "100元", "200", "300美元");
        
        // 后瞻断言 (?<=...)
        System.out.println("正向后瞻断言 (?<=...):");
        testRegex("(?<=¥)\\d+", "¥100", "100元", "$200");
        
        // 负向后瞻断言 (?<!...)
        System.out.println("负向后瞻断言 (?<!...):");
        testRegex("(?<!¥)\\d+", "¥100", "100元", "$200");
        
        // 非捕获分组 (?:...)
        System.out.println("非捕获分组 (?:...):");
        demonstrateNonCapturingGroups();
        
        System.out.println();
    }
    
    private static void demonstrateCommonPatterns() {
        System.out.println("2. 常用模式:");
        
        // IPv4 地址
        String ipv4 = "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        System.out.println("IPv4地址验证:");
        testRegex(ipv4, "***********", "***************", "256.1.1.1", "192.168.1");
        
        // MAC地址
        String mac = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$";
        System.out.println("MAC地址验证:");
        testRegex(mac, "00:1B:44:11:3A:B7", "00-1B-44-11-3A-B7", "00:1B:44:11:3A", "GG:1B:44:11:3A:B7");
        
        // 信用卡号（Luhn算法格式）
        String creditCard = "^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})$";
        System.out.println("信用卡号格式验证:");
        testRegex(creditCard, "****************", "****************", "1234567890123456");
        
        // HTML标签匹配
        String htmlTag = "<([a-zA-Z][a-zA-Z0-9]*)\\b[^>]*>(.*?)</\\1>";
        System.out.println("HTML标签匹配:");
        demonstrateHtmlMatching();
        
        // 十六进制颜色代码
        String hexColor = "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$";
        System.out.println("十六进制颜色代码:");
        testRegex(hexColor, "#FF0000", "#f00", "#GGGGGG", "FF0000");
        
        System.out.println();
    }
    
    private static void demonstratePerformanceTips() {
        System.out.println("3. 性能优化技巧:");
        
        System.out.println("避免回溯的技巧:");
        System.out.println("- 使用非捕获分组 (?:...) 而不是 (...)");
        System.out.println("- 使用字符类 [abc] 而不是 (a|b|c)");
        System.out.println("- 将最可能匹配的选项放在前面");
        System.out.println("- 使用具体的量词而不是贪婪量词");
        
        // 贪婪 vs 非贪婪示例
        System.out.println("\n贪婪 vs 非贪婪匹配:");
        demonstrateGreedyVsLazy();
        
        System.out.println();
    }
    
    private static void demonstrateNonCapturingGroups() {
        String text = "2023-12-25";
        
        // 捕获分组
        Pattern capturingPattern = Pattern.compile("(\\d{4})-(\\d{2})-(\\d{2})");
        Matcher capturingMatcher = capturingPattern.matcher(text);
        if (capturingMatcher.matches()) {
            System.out.println("捕获分组结果:");
            System.out.println("完整匹配: " + capturingMatcher.group(0));
            System.out.println("年份: " + capturingMatcher.group(1));
            System.out.println("月份: " + capturingMatcher.group(2));
            System.out.println("日期: " + capturingMatcher.group(3));
        }
        
        // 非捕获分组
        Pattern nonCapturingPattern = Pattern.compile("(?:\\d{4})-(?:\\d{2})-(?:\\d{2})");
        Matcher nonCapturingMatcher = nonCapturingPattern.matcher(text);
        if (nonCapturingMatcher.matches()) {
            System.out.println("非捕获分组结果:");
            System.out.println("完整匹配: " + nonCapturingMatcher.group(0));
            System.out.println("分组数量: " + nonCapturingMatcher.groupCount());
        }
    }
    
    private static void demonstrateHtmlMatching() {
        String html = "<div>Hello World</div>";
        Pattern pattern = Pattern.compile("<([a-zA-Z][a-zA-Z0-9]*)\\b[^>]*>(.*?)</\\1>");
        Matcher matcher = pattern.matcher(html);
        
        if (matcher.find()) {
            System.out.println("HTML标签: " + matcher.group(1));
            System.out.println("标签内容: " + matcher.group(2));
        }
    }
    
    private static void demonstrateGreedyVsLazy() {
        String text = "<div>Hello</div><span>World</span>";
        
        // 贪婪匹配
        Pattern greedyPattern = Pattern.compile("<.*>");
        Matcher greedyMatcher = greedyPattern.matcher(text);
        if (greedyMatcher.find()) {
            System.out.println("贪婪匹配: " + greedyMatcher.group());
        }
        
        // 非贪婪匹配
        Pattern lazyPattern = Pattern.compile("<.*?>");
        Matcher lazyMatcher = lazyPattern.matcher(text);
        System.out.print("非贪婪匹配: ");
        while (lazyMatcher.find()) {
            System.out.print(lazyMatcher.group() + " ");
        }
        System.out.println();
    }
    
    private static void testRegex(String regex, String... testStrings) {
        Pattern pattern = Pattern.compile(regex);
        System.out.printf("正则: %-30s ", regex.length() > 30 ? regex.substring(0, 27) + "..." : regex);
        
        for (String test : testStrings) {
            Matcher matcher = pattern.matcher(test);
            boolean matches = matcher.matches();
            System.out.printf("'%s':%s ", test, matches ? "✓" : "✗");
        }
        System.out.println();
    }
}
