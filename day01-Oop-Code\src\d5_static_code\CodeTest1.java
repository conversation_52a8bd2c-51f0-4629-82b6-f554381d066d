package d5_static_code;

import java.util.ArrayList;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/21 - 22:47
 **/
public class CodeTest1 {
    //目标：搞清楚静态代码的特别，了解应用场景

    public static String schoolName = "黑马";
    public static ArrayList<String> names = new ArrayList<>();

    //静态代码块： 有static修饰，属于类持有，与类一起优先加载，自动执行一次
    static{
        System.out.println("----static静态代码执行----");

        names.add("张三");
        names.add("李四");
    }

     public static void main(String[] args){
         System.out.println(2);


     }

}