package d2_finally;

import java.io.*;

public class FinallyDemo2 {
    public static void main(String[] args) {
        //创建字节输入流管道与源文件接通
        InputStream is = null;
        //创建一个字节输出流管道与目标文件接通
        OutputStream os = null;

        //字节流适合做一切文件的复制操作
        try {
            is = new FileInputStream("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01.png");

            //创建一个字节输出流管道与目标文件接通
            os = new FileOutputStream("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01_copy.png");


            byte[] buffer = new byte[1024];  //1KB

            int len;
            while((len=is.read(buffer))!=-1){
                os.write(buffer,0,len);
            }


        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try {
                os.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            try {
                is.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            System.out.println("复制成功");
        }
    }
}
