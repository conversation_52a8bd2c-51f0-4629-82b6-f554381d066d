package d1_Stream;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

public class StreamTest3 {
    public static void main(String[] args) {
        // 目标：学院Stream流的常见方法。
        List<String> list = new ArrayList<>();
        Collections.addAll(list, "张无忌", "周芷若", "赵敏", "张强", "张三丰");

        // 1. 过滤方法
        list.stream().filter(s -> s.startsWith("张")).forEach(System.out::println);




        // 2. 常见一个集合，存储。
        List<Movie> movies = new ArrayList<>();
        movies.add(new Movie("肖申克的救赎", 9.5, "阿米尔汗"));
        movies.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies.add(new Movie("阿甘正传", 7.5, "汤姆汉克斯"));

        //排序  集合中如果存储对象，方法一：对象类可以实现Comparable接口 重写compareTo方法，指定比较规则
        movies.stream().sorted().forEach(System.out::println);
        System.out.println("-------------------------------------------");
        movies.stream().sorted((m1,m2) -> Double.compare(m2.getScore(),m1.getScore())).forEach(System.out::println);

        System.out.println("-------------------------------------------");
        //limit取前几个
        movies.stream().limit(3).forEach(System.out::println);


        System.out.println("-------------------------------------------");
        //skip跳过前几个
        movies.stream().sorted((m1,m2) -> Double.compare(m2.getScore(),m1.getScore())) .skip(3).forEach(System.out::println);
        System.out.println("-------------------------------------------");
        //distinct 去重
        //集合    需要重写     hashCode和equals方法
        movies.stream().sorted((m1,m2)->Double.compare(m2.getScore(),m1.getScore())).distinct().forEach(System.out::println);

        System.out.println("-------------------------------------------");
        //6.  map 加工方法，把流上的数据加工成新数据
        movies.stream().map(m -> m.getName() + "     =>      " + m.getScore()).forEach(System.out::println);

        System.out.println("-------------------------------------------");
        //7.合并流
        //把两个流接起来
        Stream<String> s1 = Stream.of("a","b");
        Stream<String> s2 = Stream.of("c","d");

        Stream<String> s3 = Stream.concat(s1, s2);
        s3.forEach(System.out::println);

        System.out.println("-------------------------------------------");
        System.out.println("-------------------------------------------");
        System.out.println("-------------------------------------------");


    }
}
