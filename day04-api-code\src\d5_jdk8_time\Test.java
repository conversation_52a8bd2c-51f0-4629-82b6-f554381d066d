package d5_jdk8_time;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Description: 
 * @Author: Alhz
 * @Date: 2025/7/25 - 15:49
 **/
public class Test {
    public static void main(String[] args) {
        //高考倒计时

        String startTime = "2024-06-07 09:30:00" ;

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime ldt1 = LocalDateTime.parse(startTime, dtf);

        LocalDateTime ldt2 =LocalDateTime.now();

        Duration duration = Duration.between(ldt1 ,ldt2);

        System.out.println(duration.toDays() +"天 "+duration.toHoursPart()+"小时 "
        +duration.toMinutesPart()+"分钟 "+duration.toSecondsPart()+"秒");

    }
}