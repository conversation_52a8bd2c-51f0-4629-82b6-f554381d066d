package d12_interrface_jdk8;

public interface A {

    //默认方法  实例方法  必需用dafault修饰
    //默认用public 修饰
    public default void run(){
        go();
        System.out.println("run");
    }

    //私有方法(私有的实例方法)  jdk９才有的
    //只能当前接口内部的默认方法或者私有方法来调用
    private  void go(){
        System.out.println("go go go");
    }

    //3.静态方法
    //默认会用public修饰
    //接口的静态方法必须用接口名本身调用
    static  void inAddr(){
        System.out.println("inAddr go go");
    }
}
