package d13_extend_override2;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 15:56
 **/
public class Student {
    private String name;
    private int age;
    private char sex;
    private  double height;
    private String desc;

    public Student(){

    }

    public Student(String name,int age,char sex,double height,String desc){
        this.name=name;
        this.age=age;
        this.sex=sex;
        this.height=height;
        this.desc=desc;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public char getSex() {
        return sex;
    }

    public void setSex(char sex) {
        this.sex = sex;
    }

    public double getHeight() {
        return height;
    }

    public void setHeight(double height) {
        this.height = height;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", sex=" + sex +
                ", height=" + height +
                ", desc='" + desc + '\'' +
                '}';
    }
}