package d1_innerclass1;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 09:42
 **/
public class Test {
    public static void main(String[] args) {
        //目标:掌握成员内部类、搞清楚语法
        //成员内部类创建对象的语法

        //外部类名，内部类名 对象名 = new 外部类名（）.new 内部类名（）
        Outer.Inner inner1 = new Outer().new Inner();

         inner1.setAge(30);
         inner1.setName("s");
         inner1.show();

        People.Heart people = new People().new Heart();
        people.show();

    }
}