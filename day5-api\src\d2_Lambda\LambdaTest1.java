package d2_Lambda;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 10:34
 **/
public class LambdaTest1 {
    public static void main(String[] args) {
        //匿名内部类
        Animal a = new Animal(){
            @Override
            public void run(){
                System.out.println("动物跑");
            }
        };
        a.run();

        Swimming s2 = new Swimming(){
            @Override
            public void swin() {
                System.out.println("跳水里边喝边游泳~~~~~~~");
            }
        };
        s2.swin();

        //lambda 并不能简化所有匿名内部类的代码，只能简化函数式接口的匿名内部类

        //可以通过上下文推断出lambda的参数列表和返回值类型
        Swimming s = () -> {
            System.out.println("跳水里边喝边游泳");
        };
        s.swin();


    }
}

@FunctionalInterface    // 函数式接口 中有且仅有一个抽象方法
interface Swimming{
    void swin();
}

abstract  class Animal{
    public abstract void run();
}