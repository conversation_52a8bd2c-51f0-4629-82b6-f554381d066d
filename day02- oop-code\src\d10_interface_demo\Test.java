package d10_interface_demo;

import java.util.ArrayList;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 16:07
 **/
public class Test {
    public static void main(String[] args) {
        //目标：班级学生管理系统
        //1.每个学生是一个对象，所有需要先定义学生类，用于创建学生对象，封闭学生数据

        //2.定义接口  classDate
        ArrayList<Student> students = new ArrayList<>();
        students.add(new Student("张三",'男',95));
        students.add(new Student("钟灵",'女',75));
        students.add(new Student("李四",'男',50));
        students.add(new Student("公主",'女',98));
        students.add(new Student("虚竹",'男',90));

        //3.定义两套实现类，来分别处理，以便解耦合
//        ClassDate classDate1 = new ClassDateImpl1(students);
//        classDate1.printAllStudentInfo();
//        classDate1.printAllStudentAverageScore();

        ClassDate classDate2 = new ClassDateImpl2(students);
        classDate2.printAllStudentInfo();
        classDate2.printAllStudentAverageScore();

    }
}