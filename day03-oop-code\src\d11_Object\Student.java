package d11_Object;

import java.util.Objects;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 17:05
 **/
public class Student {
    private String name;
    private int age;
    private double score;
    public Student(){
    }

    public Student(String name, int age, double score){}{
        this.name = name;
        this.age = age;
        this.score = score;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", score=" + score +
                '}';
    }

    /*
    *  比较者    t1 ==> this
    *  被比较者  t2 ==> 0
    * */
    @Override
    public boolean equals(Object o) {
        //判断 o 如果是null 直接返回false 或者两个对象类型不一样也是 false
        if (o == null || getClass() != o.getClass()) return false;
        Student student = (Student) o;
        return age == student.age && Double.compare(score, student.score) == 0 && Objects.equals(name, student.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, age, score);
    }
}