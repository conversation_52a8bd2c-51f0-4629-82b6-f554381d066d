package d10_interface_demo;

import java.util.ArrayList;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 16:14
 **/
public class ClassDateImpl2 implements ClassDate {
    private ArrayList<Student> students;

    public ClassDateImpl2(ArrayList<Student> students) {
        this.students = students;
    }

    /*
    * 第二套实现类
    * */
    @Override
    public void printAllStudentInfo() {
        System.out.println("=== 输出所有学生信息 ===");
        int cnt = 0;
        for(int i=0;i<students.size();i++){
            if(students.get(i).getSex()=='男') cnt++;
            System.out.println(students.get(i).toString());
        }
        System.out.println("男生人数：" +cnt);
        System.out.println("女生人数："+(students.size()-cnt));
    }

    @Override
    public void printAllStudentAverageScore() {
        System.out.println("=== 输出所有学生平均成绩 ===");
        double sum=0;

        double score=students.get(0).getScore();
        double max = score;
        double min = score;

        for(int i=0;i<students.size();i++){
            sum+=students.get(i).getScore();
            if(students.get(i).getScore()>max) max = students.get(i).getScore();
            if(students.get(i).getScore()<min) min = students.get(i).getScore();
        }
        double avg=sum/students.size();
        System.out.println(avg);

        System.out.println("最高分："+max);
        System.out.println("最低分："+min);
    }
}