package d3_method_reference;

import java.util.Arrays;
import java.util.Comparator;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 13:40
 **/
public class Test3 {
    public static void main(String[] args) {
        //目标：特定类型的方法引用
        String[] name = {
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
        };

        //对他们排序 默认是按照首字母字典顺序
        //拓展 忽略大小写排序
        //如果某个lanbda 表达工只是调用一个实例方法   并且前面参数列表中的第一个参数是作为就去的主调，
        //后面所有 参数都是 作为该 实例方法的入参的，则此时就可以使用特定类型的方法引用
        //Arrays.sort(name, ( o1,  o2) -> o1.compareToIgnoreCase(o2));
        Arrays.sort(name,String::compareToIgnoreCase);



        System.out.println(Arrays.toString(name));


    }
}