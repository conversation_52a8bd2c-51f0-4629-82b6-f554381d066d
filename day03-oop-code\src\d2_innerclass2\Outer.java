package d2_innerclass2;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 10:57
 **/
public class Outer {

    public static String schoolName = "黑马";

    public static void inAddr(){
        System.out.println("我在黑马");
    }


    private double height;

    //静态内部类
    //特点：有static修饰，属于外部类本身所有
    public static class Inner {
        private String name;
        private int age;

        public Inner() {
        }
        public Inner(String name, int age) {
            this.name = name;
            this.age = age;
        }

        //拓展：成员内部类访问外部类的成员特点
        public void show(){
            //1.静态内部类中，是否可以直接访问外部类的静态成员? 可以
            System.out.println(schoolName);
            inAddr();
            //静态内部类中，是否可以直接访问外部类的实例成员???  不可以
            //System.out.println(height); // 报错
            /* 借助访问
            Outer o =  new Outer();
            System.out.println(o.height);
            */
        }

        public String getName() {
            return name;
        }
        public void setName(String name) {
            this.name = name;
        }
        public int getAge() {
            return age;
        }
        public void setAge(int age) {
            this.age = age;
        }

    }
}