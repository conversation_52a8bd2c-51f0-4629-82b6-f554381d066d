package d5_static_code;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 10:10
 **/
public class CodeTest2 {
    private String name;
    //实例代码块，属于类的每个对象的，每次创建对象时都会自动执行，在构造器之前
    //实例变量初始化
    {
        System.out.println("===实例代码块执行===");
        name =" 张三";
    }

    public CodeTest2() {
        System.out.println("===构造器执行===");
    }

    public static void main(String[] args) {
        //目标：搞清楚实例代码块的作用，应用场景
        //实例代码块 ==  对象
        //作用：和构造器一样，都是用来完成对象的初始化
        new CodeTest2();
    }
}