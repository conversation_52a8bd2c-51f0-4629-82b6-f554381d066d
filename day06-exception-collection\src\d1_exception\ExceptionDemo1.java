package d1_exception;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.logging.SimpleFormatter;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/28 - 18:57
 **/
public class ExceptionDemo1 {
    public static void main(String[] args) {
        // 1.认识导演
        //运行异常   编译异常
        System.out.println("===开始===");

        int[] arr = {11,22,33};
        System.out.println(arr[0]);
        System.out.println(arr[1]);
        System.out.println(arr[2]);
        //System.out.println(arr[3]); //数组索引超出边界异常  arrayindexoutofboundsexception



        String name = null;
        //System.out.println(name.length());// nullPointerException 空指针异常


        //System.out.println(10/0);  //ArithemtiException 算数异常

//        Object o = "张三";
//        Integer i = (Integer) o;
        //System.out.println(i);   //ClassCastException   类型转换

        /*String s = "23a";
        int it = Integer.valueOf(s); //NumberFormatException
        System.out.println(it);*/

        parseDate("2024-03-19 13:20:31");

        System.out.println("===结束===");
    }
    public static void parseDate(String s){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
       // Date d = sdf.parse(s); //编译时异常 ParseException  写代码时就报错   担心这里存在问题  将异常抛出或捕获


    }
}