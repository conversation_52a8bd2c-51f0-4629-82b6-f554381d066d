package d4_Charset;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.Arrays;

public class CharSetDemo1 {
    public static void main(String[] args) throws Exception {
        String info = "abc路易十三的头!12";

        //GBK 英文数字占一个字节    汉字两个字节
        //UTF-8  英文数字 1字节  汉字 3字节
        //UTF-32  都用4字节表示

        //编码成字符
        byte[] bytes = info.getBytes();  //默认用平台编码UTF-8编码
        System.out.println(Arrays.toString(bytes));


        byte[] bytes1 = info.getBytes("GBK");  //默认用平台编码UTF-8编码
        System.out.println(Arrays.toString(bytes1));

        byte[] bytes2 = info.getBytes("UTF-32");
        System.out.println(Arrays.toString(bytes2));

        //2.解码

        String rs1 = new String(bytes);
        System.out.println(rs1);

        //指定编码格式
        String rs2 = new String(bytes1,"GBK");
        System.out.println(rs2);


    }
}
