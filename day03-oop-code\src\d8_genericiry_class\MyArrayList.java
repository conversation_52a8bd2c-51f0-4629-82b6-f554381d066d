package d8_genericiry_class;

import java.lang.reflect.Array;
import java.util.ArrayList;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 16:45
 **/
public class MyArrayList <E>{
    private ArrayList list = new ArrayList();

    public boolean add(E e){
        list.add(e);
        return true;
    }

    public boolean remove(E e){
        return list.remove(e);
    }

   @Override
    public String toString(){
        return list.toString();
   }

}