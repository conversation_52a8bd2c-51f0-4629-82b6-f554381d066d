package d5_jdk8_time;

import java.time.Instant;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 15:16
 **/
public class InstantTest {
    public static void main(String[] args) {

        //创建对象，获取此刻时间信息
        //世界标准时间UTC
        Instant now = Instant.now();
        System.out.println(now);

        //获取总秒数
        System.out.println(now.getEpochSecond());

        //获取不够1秒的纳秒数
        System.out.println(now.getNano());
    }
}