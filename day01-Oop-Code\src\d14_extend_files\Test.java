package d14_extend_files;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 16:18
 **/
public class Test {
    public static void main(String[] args) {
        //目标：  子类访问成员的原则
        Zi zi = new Zi();
        zi.showName();
    }
}

class Zi extends Fu{
    String name = "子类名称";
    public void showName(){
        String name = "局部名称";
        System.out.println(name);
        System.out.println(this.name);
        //使用super 访问父类的name
        System.out.println(Zi.super.name);
    }
}

class Fu{
    String name = "父类名称";
}