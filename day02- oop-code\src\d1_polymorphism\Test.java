package d1_polymorphism;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 19:56
 **/
public class Test {
    public static void main(String[] args) {
        //认识多态，搞清楚多态的使用前提
        //方法：编译看左边，运行看右边
        Animals a = new Cat();
        a.cry();
        //成员变量：编译看左边，运行也看左边    变量不存在多态性
        System.out.println(a.name);

        Animals b = new Dog();
        b.cry();
        System.out.println(b.name);
    }
}