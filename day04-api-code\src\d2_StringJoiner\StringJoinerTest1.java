package d2_StringJoiner;

import java.util.StringJoiner;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 10:18
 **/
public class StringJoinerTest1 {
    public static void main(String[] args) {
        //使用StringJoiner 完成对字符串的拼接操作
        int[] arr = {1, 2, 3, 4};

        System.out.println(getArrayData(arr));

    }

    public static String getArrayData(int[] arr) {
        //创建一个StringJoiner对象
        //间隔符  开始符   结束符
        StringJoiner sb = new StringJoiner(",","[","]");

        for (int i = 0; i < arr.length-1; i++) {
            sb.add(Integer.toString(arr[i]));
        }

        return sb.toString();
    }
}