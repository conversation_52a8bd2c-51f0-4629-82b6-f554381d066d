package d16_extends_constructor2;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 17:08
 **/
public class Test {
    public static void main(String[] args) {
        //子类为啥要调用父类构造器，初始化继承父类的部分数据
        Teacher teacher = new Teacher();
        teacher.setName("张三");
        teacher.setAge(35);
        teacher.setSkill("Java安慰师");

        System.out.println(teacher.getName());
        System.out.println(teacher.getAge());
        System.out.println(teacher.getSkill());

        Teacher teacher1 = new Teacher("李四",34,"java吹牛逼");

        System.out.println(teacher1.getName());
        System.out.println(teacher1.getAge());
        System.out.println(teacher1.getSkill());
    }
}