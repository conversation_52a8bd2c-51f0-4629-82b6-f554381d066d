package d13_interface;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 19:12
 **/
public class Test {
    public static void main(String[] args) {
        //注意事项

        /*Cat cat = new Cat();
        cat.run();*/
    }
}

//一个接口继承多个接口，如果多个接口中存在方法签名冲突，则此时不支持多继承   ->  返回值类型不同，不支持多继承
/*
interface A{
    String run();
}

interface B{
    void run();
}

interface C extends A,B{}
*/

//2.一个接口实现多个接口，如果多个接口中存在方法签名冲突，则此时不支持多实现   ->  返回值类型不同，不支持多实现
/*
interface A{
    String run();
}

interface B{
    void run();
}

interface C implements A,B{}*/



/*
//3.一个类继承了父类，又同时实现了接口，父类中和接口中有同名的默认方法，实现类会优先用父类的
class Animal{
    public void run(){
        System.out.println("animal run");
    }
}

interface  GO{
    default void run(){
        System.out.println("Go run");
    }
}
class Cat extends Animal implements GO{

    public void test(){
        GO.super.run();   //找go中的run 方法
    }
}

*/

/*
//4.一个类实现多个接口，多个接口中存在同名的默认方法，可以不冲突，这个类重写该方法即可
interface A3{
    default void run(){
        System.out.println("A3 run run");
    }
}
interface B3{
    default void run(){
        System.out.println("B3 run run");
    }
}

class C3 implements A3,B3{
    @Override
    public void run(){
        System.out.println("C3 run run");
        A3.super.run();
        B3.super.run();
    }

}*/
