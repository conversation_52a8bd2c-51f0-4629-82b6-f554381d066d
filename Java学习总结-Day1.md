# Java学习总结 - Day1: Static静态特性与继承机制

## 📌 当日核心学习目标
- 理解static关键字的作用机制和应用场景
- 掌握Java继承的实现原理和使用规范
- 熟练运用访问修饰符控制成员访问权限
- 理解方法重写和构造器调用规则
- 掌握单例设计模式的实现方式

## 🎯 主要知识点详解

### 1. Static静态特性

#### 1.1 静态变量 (Static Variables)
**核心概念：**
- 使用`static`关键字修饰的成员变量
- 在内存中只有一份，被类和所有对象共享
- 随类加载而加载，优先于对象创建

**语法格式：**
```java
public class Student {
    static String schoolName = "黑马程序员";  // 静态变量
    int age;  // 实例变量
}
```

**访问方式：**
```java
// 推荐方式：类名.静态变量
Student.schoolName = "新学校名";

// 不推荐：对象名.静态变量
Student s = new Student();
s.schoolName = "新学校名";  // 不推荐
```

**关键特点：**
- 内存共享：所有对象共享同一份静态变量
- 类级别：属于类，不属于任何具体对象
- 生命周期：随类加载创建，随类卸载销毁

#### 1.2 静态方法 (Static Methods)
**核心概念：**
- 使用`static`关键字修饰的方法
- 可以直接通过类名调用，无需创建对象
- 常用于工具类和通用功能实现

**语法格式：**
```java
public class MathUtil {
    public static int add(int a, int b) {
        return a + b;
    }
}

// 调用方式
int result = MathUtil.add(10, 20);
```

**访问规则：**
- 静态方法只能直接访问静态成员
- 静态方法中不能使用this关键字
- 实例方法可以访问所有成员（静态+实例）

#### 1.3 静态代码块 (Static Code Blocks)
**核心概念：**
- 类加载时自动执行，且只执行一次
- 用于初始化静态变量和静态资源

**语法格式：**
```java
public class InitDemo {
    static String data;
    
    // 静态代码块
    static {
        System.out.println("静态代码块执行");
        data = "初始化数据";
    }
    
    // 实例代码块
    {
        System.out.println("实例代码块执行");
    }
}
```

### 2. 单例设计模式（基于实际项目代码）

#### 2.1 饿汉式单例
**实际项目代码（A.java）：**
```java
public class A {
    // 2.静态变量用来记住类的唯一对象   私有化防止修改
    private static A a = new A();

    // 1.把类的构造器私有处理
    private A(){
    }

    // 3.提供静态方法返回唯一对象     通过一个公共的方法提供对象数据
    public static A getInstance(){
        return a;
    }
}
```

#### 2.2 懒汉式单例
**实际项目代码（AA.java）：**
```java
public class AA {
    private static AA a;

    private AA(){
    }

    public static AA getInstance(){
        // 第一次来拿，创建对象，后续不再创建对象
        if(a == null){
            a = new AA();
        }
        return a;
    }
}
```

**单例模式测试：**
```java
public class SingletonTest {
    public static void main(String[] args) {
        // 测试饿汉式单例
        A a1 = A.getInstance();
        A a2 = A.getInstance();
        System.out.println("饿汉式单例测试：" + (a1 == a2)); // true
        
        // 测试懒汉式单例
        AA aa1 = AA.getInstance();
        AA aa2 = AA.getInstance();
        System.out.println("懒汉式单例测试：" + (aa1 == aa2)); // true
    }
}
```

**对比分析：**
| 特性 | 饿汉式 | 懒汉式 |
|------|--------|--------|
| 创建时机 | 类加载时 | 第一次使用时 |
| 线程安全 | 天然线程安全 | 需要同步处理 |
| 内存占用 | 可能浪费内存 | 节省内存 |
| 性能 | 无同步开销 | 有同步开销 |

### 3. 继承机制 (Inheritance)

#### 3.1 基础继承概念
**核心概念：**
- 使用`extends`关键字实现类的继承关系
- 子类自动获得父类的非私有成员
- 实现代码复用和建立类的层次结构

**语法格式：**
```java
// 父类
public class Animal {
    protected String name;
    private int age;  // 私有成员，子类不能直接访问
    
    public Animal(String name) {
        this.name = name;
    }
    
    public void eat() {
        System.out.println(name + "正在吃东西");
    }
}

// 子类
public class Dog extends Animal {
    private String breed;
    
    public Dog(String name, String breed) {
        super(name);  // 调用父类构造器
        this.breed = breed;
    }
    
    @Override
    public void eat() {
        System.out.println(name + "正在吃狗粮");
    }
    
    public void bark() {
        System.out.println(name + "正在汪汪叫");
    }
}
```

#### 3.2 方法重写 (Method Overriding)
**核心规则：**
- 使用`@Override`注解标识重写方法
- 方法签名必须完全相同（方法名、参数列表、返回值类型）
- 访问权限不能比父类更严格
- 不能重写private、final、static方法

**重写示例：**
```java
public class Cat extends Animal {
    @Override
    public void eat() {
        System.out.println(name + "正在吃鱼");
    }
    
    // 重写toString方法
    @Override
    public String toString() {
        return "Cat{name='" + name + "'}";
    }
}
```

#### 3.3 构造器调用规则
**核心原则：**
- 子类构造器必须先调用父类构造器
- 使用`super()`显式调用父类构造器
- 如果不显式调用，编译器自动调用父类无参构造器

**构造器链示例：**
```java
public class Vehicle {
    protected String brand;
    
    public Vehicle() {
        System.out.println("Vehicle无参构造器");
    }
    
    public Vehicle(String brand) {
        this.brand = brand;
        System.out.println("Vehicle有参构造器：" + brand);
    }
}

public class Car extends Vehicle {
    private int doors;
    
    public Car() {
        super();  // 调用父类无参构造器
        System.out.println("Car无参构造器");
    }
    
    public Car(String brand, int doors) {
        super(brand);  // 调用父类有参构造器
        this.doors = doors;
        System.out.println("Car有参构造器：" + doors + "门");
    }
}
```

### 4. 访问修饰符

**访问权限对比：**
| 修饰符 | 同一类 | 同一包 | 不同包子类 | 不同包非子类 |
|--------|--------|--------|------------|--------------|
| private | ✓ | ✗ | ✗ | ✗ |
| 默认(包私有) | ✓ | ✓ | ✗ | ✗ |
| protected | ✓ | ✓ | ✓ | ✗ |
| public | ✓ | ✓ | ✓ | ✓ |

## 💻 实际代码示例

### 工具类设计（基于实际项目代码）
```java
public class IteimaUtil {
    //工具类没有创建对象的需求，需要私有构建器
    private IteimaUtil() {
    }

    public static String createCode(int cnt){
        String data="ABCDEFGHIJKLMNOPQRSRUVWXYZabcdefghijklmnopqrstuvwxyz123456789";
        String code="";
        Random random=new Random();
        for(int i=0;i<cnt;i++){
            int index=random.nextInt(data.length());
            code+=data.charAt(index);
        }
        return code;
    }
}

// 使用示例
public class Login {
    public static void main(String[] args) {
        System.out.println(IteimaUtil.createCode(5));
    }
}
```

## 🔍 重点难点分析

### 1. 静态成员的内存模型
- **关键理解**：静态成员属于类，在方法区中只有一份
- **常见误区**：认为每个对象都有自己的静态变量副本
- **实际情况**：所有对象共享同一份静态变量

### 2. 继承中的成员访问
- **就近原则**：局部变量 → 子类成员 → 父类成员
- **this vs super**：this指向当前对象，super指向父类
- **重写规则**：子类重写方法会覆盖父类方法

### 3. 构造器调用链
- **执行顺序**：父类构造器 → 子类构造器
- **隐式调用**：不写super()时自动调用父类无参构造器
- **注意事项**：父类没有无参构造器时必须显式调用有参构造器

## 📝 当日学习总结和要点回顾

### 🎯 Static特性要点
1. **内存特点**：静态成员在内存中只有一份，类级别共享
2. **加载时机**：随类加载而加载，优先于对象创建
3. **访问方式**：推荐使用类名直接访问
4. **应用场景**：工具类、单例模式、共享数据
5. **使用限制**：静态方法只能访问静态成员，不能使用this

### 🎯 继承机制要点
1. **代码复用**：子类自动获得父类的非私有成员
2. **层次结构**：建立类之间的is-a关系
3. **方法重写**：子类可以重新定义父类方法的行为
4. **构造顺序**：先调用父类构造器，再调用子类构造器
5. **访问控制**：通过修饰符控制继承的范围
6. **成员访问**：遵循就近原则（局部→子类→父类）

### 🎯 设计模式应用
- **单例模式**：确保类只有一个实例，提供全局访问点
- **工具类设计**：私有构造器 + 静态方法，提供通用功能
- **模板方法**：在父类中定义算法骨架，子类实现具体步骤

---

**下一步学习预告：** Day2将学习多态机制和Final关键字，深入理解面向对象的核心特性。
