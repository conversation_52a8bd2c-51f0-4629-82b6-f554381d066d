package d7_genericity;

import java.util.ArrayList;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 15:41
 **/
public class Test {
    public static void main(String[] args) {
        //目标;认识泛型
        //定义类 接口 方法时，同时声明了一个或者多个类型变量 如<E>
        //本质：是把具体的数据类型作为参数传给类型变量
        ArrayList<String> list = new ArrayList();
//        list.add(1);
//        list.add(2);
//        list.add(true);
        list.add("asdfa");

        //开发需要统一数据类型
        for(int i =0 ;i<list.size();i++){
            String ele  = list.get(i);
            System.out.println(ele);
        }
    }
}