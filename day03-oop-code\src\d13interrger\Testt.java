package d13interrger;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 19:53
 **/
public class Testt {
    public static void main(String[] args) {
        int a = 12;

        //封装
        //1.把基本数据类型通过包装类包装成对象
        //Integer b = new Integer(a);
        //如果需要封装的数据大小在 -128 ~  127 之前直接从缓存中取
        //如果值不在范围之内  则重新进行 new
        //手动包装
        Integer b = Integer.valueOf(a);
        System.out.println(b);

        //2. 自动装箱机制  基本类型的数据可以直接变成对象赋值给包装类
        Integer it2 = 128;
        Integer it3 = 128;
        System.out.println(it2 == it3);


        //3.自动拆箱  包装类可以直接赋值给基本数据类型
        int c = it2;
        int d = it3;
        System.out.println(c);


        System.out.println("------------------------------------");
        System.out.println("包装类的功能");

        //1.功能一 包装类只可以把基本数据类型转换成字符串
        Integer it4 = 123;
        String str = it4.toString();
        System.out.println(str+1);

        int a1 = 123;
        String rs3 = a1+"";
        System.out.println(rs3+1);

        //2.功能二  ： 把字符串数值转换成基本数据类型
        String str1 = "123";
        int num = Integer.parseInt(str1);
        System.out.println(num+1);

        String str2 = "99.5";
        double score = Double.parseDouble(str2);
        System.out.println(score);

        //3.由于泛型和集合都不支持基本数据类型，因些包装类在集合与泛型中大量使用

    }
}