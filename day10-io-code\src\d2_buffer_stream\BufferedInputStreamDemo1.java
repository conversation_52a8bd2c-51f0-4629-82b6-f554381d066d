package d2_buffer_stream;

import java.io.*;

public class BufferedInputStreamDemo1 {
    public static void main(String[] args) {
        //字节流适合做一切文件的复制操作
        try(
                //用完之后自动释放资源对象   用完后会自动调用资源的close 方法关闭资源
                //创建字节输入流管道与源文件接通
                InputStream is = new FileInputStream("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01.png");
                //使用高级的缓冲字节输入流包装低级的字节输入流
                InputStream bis = new BufferedInputStream(is);
                //创建一个字节输出流管道与目标文件接通
                OutputStream os = new FileOutputStream("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01_copy.png");

                OutputStream bos = new BufferedOutputStream(os);
        ) {

            byte[] buffer = new byte[1024];  //1KB

            int len;
            while((len=bis.read(buffer))!=-1){
                bos.write(buffer,0,len);
            }

        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
