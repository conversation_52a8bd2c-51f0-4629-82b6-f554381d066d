package d5_list;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 14:18
 **/
public class ListDemo1 {
    public static void main(String[] args) {
        //目标：掌握list集合的特有功能
        List<String> list  = new ArrayList<>();
        list.add("a");
        list.add("aa");
        list.add("aaa");
        list.add("aaaa");
        System.out.println("初始数组："+list);

        //1.插入一个元素
        list.add(4,"dddd");
        System.out.println("插入4索引的元素后："+list);

        //2.根据索引删除元素
        System.out.println(list.remove(2));
        System.out.println("删除2索引的元素后："+list);

        //3.修改索引位置处的元素
        System.out.println(list.set(1, "dd"));
        System.out.println("修改1索引的元素后："+list);

        //4.根据索引取元素
        System.out.println("获取1索引元素："+list.get(1));

        //四种遍历

    }
}