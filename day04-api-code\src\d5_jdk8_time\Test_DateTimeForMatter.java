package d5_jdk8_time;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 15:32
 **/
public class Test_DateTimeForMatter {
    public static void main(String[] args) {
        //创建一个时间格式
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss EEE a");

        //对时间进行格式化
        LocalDateTime ld = LocalDateTime.now();
        String result ;
        result = dtf.format(ld);
        System.out.println(result);


        //格式化时间
        String result2  = ld.format(dtf);
        System.out.println(result2);


        //4.解析时间：解析时间一般使用   localDateTime提供的解析方法来解析
        String dateStr = "2023-11-11 11:11:11";
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime ld2 = LocalDateTime.parse(dateStr, dtf2);
        System.out.println(ld2);

    }
}