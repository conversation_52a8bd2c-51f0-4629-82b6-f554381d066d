package d1_char_stream;


import java.io.FileReader;
import java.io.Reader;

public class FileReaderDemo1 {
    public static void main(String[] args) throws Exception {
        //目标：掌握字符输入流每次读取一个字符的形式
        //1.创建字符输入流管道与源文件接通
        Reader fr = new FileReader("day10-io-code/src/dei01.txt");    //多态

        /*//读取一个字符   没有字符可读返回-1
        int c1 = fr.read();
        System.out.println(c1);

        int c2 = fr.read();
        System.out.println(c2);

         //输出 -1  结束
        int c3 = fr.read();
        System.out.println(c3);*/

        //使用循环来读取
        int c;
        while((c = fr.read())!=-1){
            System.out.print((char) c);
        }

        //拓展
    }
}
