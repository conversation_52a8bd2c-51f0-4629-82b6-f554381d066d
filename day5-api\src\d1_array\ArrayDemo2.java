package d1_array;

import java.util.Arrays;
import java.util.Comparator;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 09:23
 **/
public class ArrayDemo2 {
    public static void main(String[] args) {
        Student[]   students  = new Student[4];
        students[0] = new Student("张三",18,'男',1.75);
        students[1] = new Student("李四",22,'男',1.80);
        students[2] = new Student("王五",20,'男',1.85);
        students[3] = new Student("赵六",19,'男',1.90);
        /*没有规则来排序对象
        Arrays.sort(students);
        System.out.println(Arrays.toString(students));*/

        //自定义排序规则方式一:  让对象所在的类实现比较规则接口 Comparator  重写compare方法
        //sort方法内部会拿 重写后的compare 来进行比较
        Arrays.sort(students);
        System.out.println(Arrays.toString(students));


        //自定义排序规则方式二: sort 存在重载的方法，支持自带的 Comparator 比较对象对象来直接指定比较规则
        //public static <T> void sort(T[] a, comparator<? super T> c)
        //Comparator  比较器  比较对象
        //compare方法  比较两个对象的大小
        //o1  o2
        //o1-o2  升序
        //o2-o1  降序
        Arrays.sort(students, new Comparator<Student>() {
            @Override
            public int compare(Student o1, Student o2) {
                //需要注意数据的类型
                if(o1.getAge() == o2.getAge()){
                    return 0;
                }
                else if (o1.getAge() > o2.getAge()){
                    return 1;
                }
                return -1;
                //return o2.getAge() - o1.getAge();
                //return Double.compare(o1.getHeight(), o2.getHeight());    比较double类型的数据需要注意   不可以用强制类型转换
            }
        });

        System.out.println(Arrays.toString(students));
    }
}