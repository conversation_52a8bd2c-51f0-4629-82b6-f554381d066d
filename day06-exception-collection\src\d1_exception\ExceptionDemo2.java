package d1_exception;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/28 - 19:27
 **/
public class ExceptionDemo2 {
    public static void main(String[] args) {
        //搞清楚异常的作用
        //异常是用来查寻系统bug的关键参考信息
        //异常可以作为方法内部的一种特殊返回值，以便通知上层调用者底层的执行情况
        try {
            //监视代码
            System.out.println(divide(1,0));
        } catch (Exception e) {
            //捕获异常 并打出这个异常信息
            throw new RuntimeException(e);
        }
    }
    public static int divide(int a ,int b){
        if(b==0){
            System.out.println("参数存在问题");
            //抛出异常 作为返回值，通知上层，出现了bug
            throw new RuntimeException("/by 0");
        }
        int c = a / b;
        return c;
    }
}