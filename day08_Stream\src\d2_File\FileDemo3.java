package d2_File;

import java.io.File;
import java.io.IOException;

public class FileDemo3 {
    public static void main(String[] args) throws Exception {
        File f = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\Test1.txt");

        //1.  createNewfile() 创建一个新文件（内容为空），创建成功返回 true 否则为 false
        System.out.println(f.createNewFile());

        //2.  mkdir 创建文件夹
        //默认只能创建一级
        File f2 = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\Test2");
        System.out.println(f2.mkdir());

        //mkdirs 可以创建多级
        File f3 = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\a\\b\\c");
        System.out.println(f3.mkdirs());

        //3.delete 删除 只能删除文件夹和空文件夹，不能删除非空文件夹
        //删除是不进回收站的
        File f4 = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\a\\b\\c");
        System.out.println(f4.delete());

        System.out.println( f2.delete());
    }
}
