package d6_enum2;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 15:30
 **/

public class Test1 {
    public static void main(String[] args) {
        //掌握枚举应用场景
        //需求给出一个数字，可以完成向下取整，向上取整，四舍一篇，去年小数

        //枚举做信息标准和分类：参数值能够约束
        System.out.println(handleDdate(3.1,Constant2.DELETE));

    }
    public static double handleDdate(double number,Constant2 type){
        switch (type){
            case DOWN:
                //向下取整
                number = Math.floor(number);
                break;
            case UP:
                //向上取整
                number = Math.ceil(number);
                break;
            case HAKF:
                number = Math.round(number);
                break;
            case DELETE:
                number = (int)(number);
                break;

        }
        return number;
    }
}