package d4_innerclass4_2;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 14:06
 **/
public class Test2 {
    public static void main(String[] args) {
        //匿名内部类的使用场景，通过作为一个对象参数传输给方法使用


        Swimming s1 = new Swimming() {
            @Override
            public void swin() {
                System.out.println("学生在喝水");
            }
        };
        go(s1);

        go(new Swimming() {
            @Override
            public void swin() {
                System.out.println("老师沉底了");
            }
        });
    }

    public static void go(Swimming s){
        System.out.println("开始");
        s.swin();
        System.out.println("结束\n");
    }

}


//需求： 学生和老师一起参加游泳比赛

interface Swimming{
    void swin();
}