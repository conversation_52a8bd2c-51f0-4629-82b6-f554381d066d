package d3_static_attentiion;

/**
 * @Description: 演示static关键字的使用注意事项
 * @Author: Alhz
 * @Date: 2025/7/21 - 19:50
 **/
public class Test {

    // ==================== 静态成员（类成员）====================
    // 静态变量（可以修改）
    public static String schoolName = "黑马程序员";
    private static int studentCount = 100;

    // 常量（不可修改）
    public static final String SCHOOL_TYPE = "IT培训机构";  // 真正的常量
    public static final int MAX_CAPACITY = 2000;           // 真正的常量

    public static void showSchoolInfo(){
        System.out.println("学校名称：" + schoolName);
        System.out.println("学生总数：" + studentCount);
    }

    // ==================== 实例成员（对象成员）====================
    private String name = "张三";
    private int age = 18;

    public void showStudentInfo(){
        System.out.println("学生姓名：" + name);
        System.out.println("学生年龄：" + age);
    }

    // ==================== 静态方法访问规则演示 ====================
    /**
     * 静态方法只能直接访问静态成员，不能直接访问实例成员
     */
    public static void testStaticMethod(){
        System.out.println("=== 静态方法访问测试 ===");

        // ✅ 可以访问静态变量
        System.out.println("静态变量 schoolName: " + schoolName);
        System.out.println("静态变量 studentCount: " + studentCount);

        // ✅ 可以调用静态方法
        showSchoolInfo();

        // ❌ 不能直接访问实例变量（会编译错误）
        // System.out.println(name);  // 编译错误
        // System.out.println(age);   // 编译错误

        // ❌ 不能直接调用实例方法（会编译错误）
        // showStudentInfo();  // 编译错误

        // ❌ 不能使用this关键字（会编译错误）
        // System.out.println(this.name);  // 编译错误

        // ✅ 如果要访问实例成员，必须创建对象
        Test obj = new Test();
        System.out.println("通过对象访问实例变量: " + obj.name);
        obj.showStudentInfo();
    }

    // ==================== 实例方法访问规则演示 ====================
    /**
     * 实例方法可以访问所有成员（静态成员 + 实例成员）
     */
    public void testInstanceMethod(){
        System.out.println("=== 实例方法访问测试 ===");

        // ✅ 可以访问静态变量
        System.out.println("静态变量 schoolName: " + schoolName);
        System.out.println("静态变量 studentCount: " + studentCount);

        // ✅ 可以调用静态方法
        showSchoolInfo();

        // ✅ 可以访问实例变量
        System.out.println("实例变量 name: " + name);
        System.out.println("实例变量 age: " + age);

        // ✅ 可以调用实例方法
        showStudentInfo();

        // ✅ 可以使用this关键字
        System.out.println("使用this访问: " + this.name);
        this.showStudentInfo();
    }

    /**
     * 演示静态变量 vs 常量的区别
     */
    public static void demonstrateStaticVsConstant() {
        System.out.println("=== 静态变量 vs 常量演示 ===");

        // 静态变量可以修改
        System.out.println("修改前 schoolName: " + schoolName);
        schoolName = "新东方";  // ✅ 可以修改
        System.out.println("修改后 schoolName: " + schoolName);

        System.out.println("修改前 studentCount: " + studentCount);
        studentCount = 200;  // ✅ 可以修改
        System.out.println("修改后 studentCount: " + studentCount);

        // 常量不能修改
        System.out.println("常量 SCHOOL_TYPE: " + SCHOOL_TYPE);
        System.out.println("常量 MAX_CAPACITY: " + MAX_CAPACITY);
        // SCHOOL_TYPE = "其他机构";  // ❌ 编译错误！常量不能修改
        // MAX_CAPACITY = 3000;      // ❌ 编译错误！常量不能修改

        System.out.println("\n总结：");
        System.out.println("- public static 修饰的是静态变量，可以修改");
        System.out.println("- public static final 修饰的才是常量，不能修改");
    }

    public static void main(String[] args) {
        System.out.println("=== Static使用注意事项演示 ===\n");

        // 0. 演示静态变量 vs 常量的区别
        demonstrateStaticVsConstant();

        System.out.println("\n" + "=".repeat(50) + "\n");

        // 1. 测试静态方法的访问规则
        testStaticMethod();

        System.out.println("\n" + "=".repeat(50) + "\n");

        // 2. 测试实例方法的访问规则（需要创建对象）
        Test test = new Test();
        test.testInstanceMethod();

        System.out.println("\n=== 总结 ===");
        System.out.println("1. 静态方法只能直接访问静态成员");
        System.out.println("2. 实例方法可以访问所有成员");
        System.out.println("3. 静态方法中不能使用this关键字");
        System.out.println("4. 要在静态方法中访问实例成员，必须创建对象");
        System.out.println("5. public static 是静态变量，public static final 才是常量");
    }
}