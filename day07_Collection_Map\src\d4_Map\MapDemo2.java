package d4_Map;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/30 - 14:14
 **/
public class MapDemo2 {
    public static void main(String[] args) {
        Map<String ,Integer> map = new HashMap<>();   //多态：一行经典代码

        map.put("java入门到跑路",2);
        map.put("华为手表",3);
        map.put("Iphone15",10);
        map.put("mate60",10);
        map.put("mate60",15);
        map.put(null,null);

        //1.获取集合的大小
        System.out.println(map.size());

        //3.判断集合是否为空
        System.out.println(map.isEmpty());

        //4.根据键获取对应的值
        System.out.println(map.get("mate60"));

        //5.根据键删除整个数据，返回值是键对应的值
        System.out.println(map.remove("华为手表"));

        //6.判断是否包含某个键
        System.out.println(map.containsKey("mate60"));  //存在为true 不存在为false

        //7.判断是否包含某个值
        System.out.println(map.containsValue(10));


        //8.将map转成set   取键
        Set key = map.keySet();
        System.out.println(key);

        //9.将map  全部值转成 collection 集合中返回    取值
        Collection<Integer> values = map.values();
        System.out.println(values);




        //2.清空集合
        map.clear();
        System.out.println(map.size());
    }
}