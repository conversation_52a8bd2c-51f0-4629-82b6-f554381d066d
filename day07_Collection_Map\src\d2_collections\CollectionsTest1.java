package d2_collections;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 16:46
 **/
public class CollectionsTest1 {
    public static void main(String[] args) {
        //目标：掌握collections集合工具类的使用
        List<String> list = new ArrayList<String>();
        //1.addAll    public static <T> boolean addAll(Collection<? super T> c, T... elements)  为集合批量添加数据
        Collections.addAll(list, "a11", "b22", "c33");
        System.out.println(list);

        //2.shuffle   public static void shuffle(List<?> list)  打乱集合顺序   list基于数组可以打乱
        Collections.shuffle(list);
        System.out.println(list);

        //3.sort      public static <T extends Comparable<? super T>> void sort(List<T> list)  排序
        List<Student> Students = new ArrayList<>();
        Students.add(new Student("张三", 18, '男', 1.75));
        Students.add(new Student("李四", 22, '男', 1.80));
        Students.add(new Student("王五", 20, '男', 1.85));


        //方式一: 让对象的类实现 comparable接口 重写compare方法，指定大小比较规则
        Collections.sort(Students);
        System.out.println(Students);


        //4.public static <T> void sort(List<T> list,Comparator<? super T> c);
        //对list集合中元素,按照比较器对象指定的规则进行排序
        //方式二 指定 comparator比较器对象，再指定比较规则
        Collections.sort(Students,(o1,o2)-> Double.compare(o2.getHeight(),o1.getHeight()));  //降序
        System.out.println(Students);
    }
}