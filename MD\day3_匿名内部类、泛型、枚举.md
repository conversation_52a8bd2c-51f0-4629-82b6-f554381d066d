# Java内部类、泛型、枚举学习总结

## 📚 学习概览

本文档总结了Java中内部类、泛型和枚举的核心概念和应用，主要包括四种内部类类型、泛型的使用和枚举类型的特点。

## 🏠 内部类 (Inner Classes)

### 1. 成员内部类 (d1_innerclass1)

**核心概念：**
- 定义在外部类内部，没有static修饰
- 属于外部类的对象持有
- 可以直接访问外部类的所有成员（包括私有成员）

**完整可运行代码：d1_innerclass1\Outer.java + People.java + Test.java**

```java
// Outer.java
package d1_innerclass1;

public class Outer {
    public static String schoolName = "黑马";

    public static void inAddr() {
        System.out.println("我在黑马");
    }

    private String hobby;
    private double height;

    public void run() {
        System.out.println("run");
    }

    //成员内部类
    //特点：无static修饰，属于外部类的对象持有的
    public class Inner {
        private String name;
        private int age;

        public Inner() {
        }

        public Inner(String name, int age) {
            this.name = name;
            this.age = age;
        }

        //拓展：成员内部类访问外部类的成员特点
        public void show() {
            //1.成员内部类中，是否可以直接访问外部类的静态成员? 可以
            System.out.println(schoolName);
            inAddr();

            //可以访问内部类的实例成员
            System.out.println(hobby);
            System.out.println(height);
            run();
        }

        // getter和setter方法
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }
    }
}

// People.java
package d1_innerclass1;

public class People {
    private int hearBeat = 110;

    //成员内部类
    public class Heart {
        private int hearBeat = 95;

        public void show() {
            int hearBeat = 80;
            System.out.println(hearBeat);           // 80  - 局部变量
            System.out.println(this.hearBeat);     // 95  - 内部类成员变量
            System.out.println(People.this.hearBeat); // 110 - 外部类成员变量
        }
    }
}

// Test.java
package d1_innerclass1;

public class Test {
    public static void main(String[] args) {
        //成员内部类创建对象的语法
        //外部类名.内部类名 对象名 = new 外部类名().new 内部类名();
        Outer.Inner inner1 = new Outer().new Inner();

        inner1.setAge(30);
        inner1.setName("张三");
        inner1.show();

        People.Heart heart = new People().new Heart();
        heart.show();
    }
}
```

**运行结果：**
```
黑马
我在黑马
null
0.0
run
80
95
110
```

**关键点：**
- 创建语法：`外部类名.内部类名 对象名 = new 外部类名().new 内部类名()`
- 变量访问优先级：局部变量 > 内部类成员变量 > 外部类成员变量
- 使用`外部类名.this.变量名`访问外部类成员变量

### 2. 静态内部类 (Static Inner Class)

**核心概念：**
- 使用static修饰的内部类
- 属于外部类本身，不依赖外部类对象
- 只能直接访问外部类的静态成员

**语法特点：**
```java
public class Outer {
    private static String schoolName = "黑马";
    private String hobby = "篮球";

    // 静态内部类
    public static class StaticInner {
        public void show() {
            System.out.println(schoolName); // 可以访问外部类静态成员
            // System.out.println(hobby);   // 错误：不能直接访问实例成员
        }
    }
}

// 创建对象语法
Outer.StaticInner inner = new Outer.StaticInner();
```

**关键点：**
- 创建语法：`外部类名.内部类名 对象名 = new 外部类名.内部类名()`
- 不需要外部类对象就可以创建静态内部类对象
- 只能访问外部类的静态成员

### 3. 局部内部类 (Local Inner Class)

**核心概念：**
- 定义在方法内部的类
- 只能在定义它的方法内部使用
- 可以访问外部类成员和方法的final参数

**语法特点：**
```java
public class Outer {
    private String name = "外部类";

    public void method() {
        final String localVar = "局部变量";

        // 局部内部类
        class LocalInner {
            public void show() {
                System.out.println(name);     // 可以访问外部类成员
                System.out.println(localVar); // 可以访问final局部变量
            }
        }

        LocalInner inner = new LocalInner();
        inner.show();
    }
}
```

**关键点：**
- 只能在定义的方法内部创建和使用
- 可以访问外部类所有成员
- 只能访问方法中的final变量（JDK8后可以是effectively final）

### 4. 匿名内部类 (Anonymous Inner Class)

**核心概念：**
- 没有名字的内部类
- 通常用于实现接口或继承类的简单实现
- 常用于事件处理和回调函数

**语法特点：**
```java
// 实现接口的匿名内部类
interface Runnable {
    void run();
}

public class Test {
    public static void main(String[] args) {
        // 匿名内部类实现接口
        Runnable r = new Runnable() {
            @Override
            public void run() {
                System.out.println("匿名内部类执行");
            }
        };
        r.run();

        // 继承类的匿名内部类
        Thread t = new Thread() {
            @Override
            public void run() {
                System.out.println("线程执行");
            }
        };
        t.start();
    }
}
```

**关键点：**
- 语法：`new 接口名/类名() { 重写方法 }`
- 常用于只使用一次的简单实现
- 可以访问外部类成员和final局部变量

## 📊 内部类对比总结

### 四种内部类特点对比

| 内部类类型 | 定义位置 | 修饰符 | 创建语法 | 访问外部类成员 |
|------------|----------|--------|----------|----------------|
| **成员内部类** | 外部类内部 | 无static | `new Outer().new Inner()` | 可访问所有成员 |
| **静态内部类** | 外部类内部 | static | `new Outer.Inner()` | 只能访问静态成员 |
| **局部内部类** | 方法内部 | 无修饰符 | 方法内直接new | 可访问所有成员+final局部变量 |
| **匿名内部类** | 使用时定义 | 无修饰符 | `new 接口/类(){...}` | 可访问所有成员+final局部变量 |

### 使用场景选择

**成员内部类：**
- 内部类与外部类关系密切，需要访问外部类实例成员
- 例如：People类和Heart类的关系

**静态内部类：**
- 内部类相对独立，只需要访问外部类静态成员
- 例如：工具类的内部实现类

**局部内部类：**
- 只在某个方法内部使用的临时类
- 需要访问方法参数或局部变量

**匿名内部类：**
- 简单的接口实现或类继承
- 事件处理、回调函数
- 只使用一次的场景

---

## 🔧 泛型 (Generics)

### 1. 泛型基础概念

**核心概念：**
- 泛型是JDK5引入的特性，用于在编译时提供类型安全
- 允许在定义类、接口和方法时使用类型参数
- 避免类型转换，提高代码安全性和可读性

**基本语法：**
```java
// 泛型类
public class Box<T> {
    private T content;

    public void setContent(T content) {
        this.content = content;
    }

    public T getContent() {
        return content;
    }
}

// 使用泛型类
Box<String> stringBox = new Box<>();
stringBox.setContent("Hello");
String content = stringBox.getContent(); // 无需类型转换
```

### 2. 泛型集合应用

**常用泛型集合：**
```java
// ArrayList泛型
ArrayList<String> names = new ArrayList<>();
names.add("张三");
names.add("李四");
// names.add(123); // 编译错误，类型不匹配

// HashMap泛型
HashMap<String, Integer> scores = new HashMap<>();
scores.put("张三", 95);
scores.put("李四", 87);

// 遍历泛型集合
for (String name : names) {
    System.out.println(name); // 无需类型转换
}
```

### 3. 泛型方法

**语法特点：**
```java
public class GenericMethod {
    // 泛型方法
    public static <T> void swap(T[] array, int i, int j) {
        T temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }

    // 使用泛型方法
    public static void main(String[] args) {
        String[] names = {"张三", "李四", "王五"};
        swap(names, 0, 2);

        Integer[] numbers = {1, 2, 3};
        swap(numbers, 0, 1);
    }
}
```

### 4. 泛型通配符

**通配符类型：**
```java
// 无界通配符
List<?> list = new ArrayList<String>();

// 上界通配符
List<? extends Number> numbers = new ArrayList<Integer>();

// 下界通配符
List<? super Integer> integers = new ArrayList<Number>();
```

**实际应用：**
```java
public class WildcardExample {
    // 打印任意类型的列表
    public static void printList(List<?> list) {
        for (Object item : list) {
            System.out.println(item);
        }
    }

    // 计算数字列表的总和
    public static double sum(List<? extends Number> numbers) {
        double total = 0;
        for (Number num : numbers) {
            total += num.doubleValue();
        }
        return total;
    }
}
```

### 5. 泛型的好处

**类型安全：**
- 编译时检查类型，避免ClassCastException
- 消除强制类型转换

**代码复用：**
- 一个泛型类可以处理多种类型
- 提高代码的通用性

**性能优化：**
- 避免装箱拆箱操作
- 减少类型转换的开销

---

## 🏷️ 枚举 (Enum)

### 1. 枚举基础概念

**核心概念：**
- 枚举是一种特殊的类，用于定义常量集合
- 使用enum关键字定义
- 枚举值是该枚举类型的实例

**基本语法：**
```java
// 简单枚举
public enum Season {
    SPRING, SUMMER, AUTUMN, WINTER
}

// 使用枚举
Season season = Season.SPRING;
System.out.println(season); // 输出：SPRING
```

### 2. 枚举的高级特性

**带构造器和方法的枚举：**
```java
public enum Planet {
    MERCURY(3.303e+23, 2.4397e6),
    VENUS(4.869e+24, 6.0518e6),
    EARTH(5.976e+24, 6.37814e6),
    MARS(6.421e+23, 3.3972e6);

    private final double mass;   // 质量（千克）
    private final double radius; // 半径（米）

    // 构造器
    Planet(double mass, double radius) {
        this.mass = mass;
        this.radius = radius;
    }

    // 方法
    public double getMass() { return mass; }
    public double getRadius() { return radius; }

    // 计算表面重力
    public double surfaceGravity() {
        return 6.67300E-11 * mass / (radius * radius);
    }
}
```

### 3. 枚举常用方法

**内置方法：**
```java
public class EnumMethods {
    public static void main(String[] args) {
        // values() - 返回所有枚举值
        Season[] seasons = Season.values();
        for (Season s : seasons) {
            System.out.println(s);
        }

        // valueOf() - 根据字符串获取枚举值
        Season spring = Season.valueOf("SPRING");

        // ordinal() - 获取枚举值的序号
        System.out.println(Season.SPRING.ordinal()); // 0

        // name() - 获取枚举值的名称
        System.out.println(Season.SPRING.name()); // "SPRING"
    }
}
```

### 4. 枚举的实际应用

**状态管理：**
```java
public enum OrderStatus {
    PENDING("待处理"),
    PROCESSING("处理中"),
    SHIPPED("已发货"),
    DELIVERED("已送达"),
    CANCELLED("已取消");

    private final String description;

    OrderStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

// 使用示例
public class Order {
    private OrderStatus status;

    public void updateStatus(OrderStatus newStatus) {
        this.status = newStatus;
        System.out.println("订单状态更新为：" + newStatus.getDescription());
    }
}
```

### 5. 枚举的优势

**类型安全：**
- 编译时检查，避免无效值
- 比使用常量更安全

**可读性强：**
- 枚举名称具有语义化
- 代码更易理解和维护

**功能丰富：**
- 可以添加字段、方法和构造器
- 支持接口实现

**单例保证：**
- 每个枚举值都是单例
- 线程安全

---

## 🎯 学习成果总结

### 核心技能掌握

**内部类掌握：**
- ✅ 四种内部类的定义和使用语法
- ✅ 内部类访问外部类成员的规则
- ✅ 不同内部类的适用场景
- ✅ 匿名内部类在实际开发中的应用

**泛型应用：**
- ✅ 泛型类、泛型方法的定义和使用
- ✅ 泛型集合的类型安全特性
- ✅ 通配符的使用场景
- ✅ 泛型在代码复用中的作用

**枚举使用：**
- ✅ 枚举的定义和基本使用
- ✅ 带构造器和方法的高级枚举
- ✅ 枚举在状态管理中的应用
- ✅ 枚举相比常量的优势

### 技术特点对比

| 特性 | 内部类 | 泛型 | 枚举 |
|------|--------|------|------|
| **主要作用** | 封装和组织代码 | 类型安全 | 定义常量集合 |
| **使用场景** | 类关系密切时 | 集合、工具类 | 状态、类型定义 |
| **核心优势** | 访问外部类成员 | 编译时类型检查 | 类型安全的常量 |
| **注意事项** | 持有外部类引用 | 类型擦除 | 不能继承 |

### 最佳实践建议

**内部类使用：**
1. **优先考虑静态内部类**：如果不需要访问外部类实例成员
2. **谨慎使用成员内部类**：避免内存泄漏问题
3. **合理使用匿名内部类**：简单实现时使用，复杂逻辑建议定义具名类
4. **局部内部类适度使用**：仅在方法内部需要时使用

**泛型使用：**
1. **集合必须使用泛型**：提高类型安全性
2. **合理使用通配符**：上界用于读取，下界用于写入
3. **避免原始类型**：不要使用未参数化的泛型类型
4. **泛型方法优于泛型类**：当只有方法需要泛型时

**枚举使用：**
1. **替代常量定义**：用枚举代替public static final常量
2. **添加业务方法**：为枚举添加有意义的业务方法
3. **合理使用构造器**：为枚举值添加属性
4. **实现接口增强功能**：让枚举实现接口提供更多功能

### 常见误区避免

**内部类误区：**
- ❌ 在静态内部类中访问外部类实例成员
- ❌ 忘记内部类持有外部类引用可能导致内存泄漏
- ❌ 过度使用匿名内部类导致代码可读性差

**泛型误区：**
- ❌ 使用原始类型忽略泛型警告
- ❌ 错误理解类型擦除机制
- ❌ 通配符使用不当导致编译错误

**枚举误区：**
- ❌ 试图继承枚举类
- ❌ 在枚举中定义可变字段
- ❌ 忽略枚举的单例特性

### 实际开发应用

**内部类应用场景：**
- GUI事件处理（匿名内部类）
- 数据结构实现（静态内部类）
- 回调函数定义（匿名内部类）
- 工具类的内部实现（静态内部类）

**泛型应用场景：**
- 集合框架使用
- 工具类方法定义
- DAO层数据访问
- 框架API设计

**枚举应用场景：**
- 状态机实现
- 配置参数定义
- 错误码管理
- 业务类型分类

### 后续学习方向

这些知识为后续学习Java高级特性奠定基础：
- **Lambda表达式**：可以替代某些匿名内部类
- **Stream API**：大量使用泛型特性
- **注解处理**：枚举常用于注解参数
- **设计模式**：内部类在多种设计模式中应用