package d6_Set;

import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 16:17
 **/
public class Set_demo1 {
    public static void main(String[] args) {
        //目标：掌握set集合的特有功能
        //无序 不重复  无索引
        Set<String> set = new HashSet<>();   //多态，一行经典代码
        set.add("a");
        set.add("b");
        set.add("aaa");
        set.add("d");
        System.out.println(set);   //[aaa, a, b, d]

        //有序 不重复  无索引
        Set<String> set2 = new LinkedHashSet<>();
        set2.add("a");
        set2.add("b");
        set2.add("aaa");
        set2.add("d");
        System.out.println(set2);

        //trae set 可排序
    }
}