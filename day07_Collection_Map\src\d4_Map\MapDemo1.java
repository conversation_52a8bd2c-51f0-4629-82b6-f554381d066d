package d4_Map;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/30 - 14:07
 **/
public class MapDemo1 {
    public static void main(String[] args) {
        //集合和泛型只支持 包装类
        //map hashmap  按照键 无序，不重复，无索引 值不做要求，都可以为null
        Map<String ,Integer> map = new HashMap<>();   //多态：一行经典代码

        map.put("java入门到跑路",2);
        map.put("华为手表",3);
        map.put("Iphone15",10);
        map.put("mate60",10);
        map.put("mate60",15);
        map.put(null,null);
        System.out.println(map);




    }
}