package d6_enum2;

import java.sql.Connection;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 15:12
 **/
public class Test {
    public static void main(String[] args) {
        //掌握枚举应用场景
        //需求给出一个数字，可以完成向下取整，向上取整，四舍一篇，去年小数


        //常量作信息标识和分类，也好，但参数值没有进行约束
        System.out.println(handleDdate(3.1,Constant.DELETE));

    }
    public static double handleDdate(double number,int type){
        switch (type){
            case Constant.DOWN:
                //向下取整
                number = Math.floor(number);
                break;
            case Constant.UP:
                //向上取整
                number = Math.ceil(number);
                break;
            case Constant.HAKF_UP:
                number = Math.round(number);
                break;
            case Constant.DELETE:
                number = (int)(number);
                break;

        }
        return number;
    }
}