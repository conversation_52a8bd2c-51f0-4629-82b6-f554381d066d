package d5_enum;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 14:51
 **/
public class Test {
    public static void main(String[] args) {
        //认识枚举类，了解枚举类的特点
        A a1 = A.X;
        A a2 = A.Y;
        /*System.out.println(A.X);
        System.out.println(A.Y);
*/
        A[] as = A.values();
        for(A a : as){
            System.out.println(a);
        }

        //ordinal  拿对象的索引位置
        System.out.println(a1.ordinal());
        System.out.println(a2.ordinal());

    }
}