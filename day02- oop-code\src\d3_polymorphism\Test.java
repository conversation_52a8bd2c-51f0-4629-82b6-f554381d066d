package d3_polymorphism;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 09:51
 **/
public class Test {
    public static void main(String[] args) {
        //目标:搞清楚使用多态的好处

        Animals a = new Dog();

        a.cry();

        Dog d = (Dog)a;

        d.lookDoor();
        //强制类型转换的注意事项：只要有继承或实现关系的两个类就可以强制转换

        //编译不报错，但运行会报错，
        //Cat c = (Cat)a;




    }

    //2.多态下，父类类型作为方法的形参，可以接收一切子类对象，方法更通用
    public static void go(Animals a){
        System.out.println("开始");
        a.cry();   //对象回调

        //因此JAVA建议强制转换前，先判断对象的真实类型，再进行转换
        if(a instanceof Dog){
            Dog d2 = (Dog)a;
            d2.lookDoor();
        } else if (a instanceof Cat) {
            Cat c2 = (Cat)a;
            c2.catchFish();
        }
        System.out.println("结束");
    }
}