package d3_method_reference;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 13:40
 **/
public class Test4 {
    public static void main(String[] args) {
        //构造器引用
        //格式：类名::new
        //构造器引用的使用前提：必须有与函数式接口匹配的构造器
        /*Create c2 = new Create() {
            @Override
            public Car create(String name) {
                return new Car(name);
            }
        };*/

        //如果某个lambda 表达式里只是创建对象，并且前后参数一样，就可以使用构造器引用
        //Create c2 =name ->new Car(name);

        Create c2 = Car::new;


        Car car2 = c2.create("奔驰");
        System.out.println(car2.getName());

    }
}


@FunctionalInterface
interface Create{
    Car create(String name);
}

class Car{
    private  String name;
    public Car(){
    }

    public Car(String name){
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}