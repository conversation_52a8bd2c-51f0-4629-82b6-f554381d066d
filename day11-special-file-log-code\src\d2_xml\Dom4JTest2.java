﻿package d2_xml;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.util.ArrayList;
import java.util.List;

public class Dom4JTest2 {
    public static void main(String[] args) throws Exception {
        SAXReader saxReader = new SAXReader();

        // 2. 把xml文件读取成一个document文档对象
        Document document = saxReader.read("day11-special-file-log-code/src/contact.xml");

        // 3. 文档对象中包含了xml对象的全部，提供了方法获取数据
        Element rootElement = document.getRootElement();

        //4.准备一个联系人集合存储联系人对象
        List<Contact> contacts = new ArrayList<>();

        List<Element> sonEle = rootElement.elements("contacts");
        for(Element ele : sonEle){

        }

    }
}
