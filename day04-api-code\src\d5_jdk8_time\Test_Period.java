package d5_jdk8_time;

import java.time.Duration;
import java.time.LocalDate;
import java.time.Period;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 15:41
 **/
public class Test_Period {
    public static void main(String[] args) {
        LocalDate start = LocalDate.of(2024,3,19);
        LocalDate end = LocalDate.of(2024,10,13);

        Period period = Period.between(start, end);

        //没有太大的作用
        System.out.println(period.getDays());


    }
}