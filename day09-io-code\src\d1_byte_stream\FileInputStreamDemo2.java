package d1_byte_stream;

import java.io.FileInputStream;
import java.io.InputStream;

public class FileInputStreamDemo2 {
    public static void main(String[] args) throws Exception {
        InputStream is = new FileInputStream("day09-io-code/src/dei02.txt");     //简洁写法

        //2.每次读取一个字节数组的字节，会返回读取的字节个数，没有字节可读返回 -1
        // public int readd(byte[] buffer)


        //读取都
      /*  byte[] buffer = new byte[3];
        int len = is.read(buffer);
        System.out.println("内容："+new String(buffer));
        System.out.println("内容长度："+len);

        int len1 = is.read(buffer);
        //读取多少个字节 倒出多少   offset 从桶的哪个位置开始倒   len2 读取了多少个字节
        System.out.println("内容：" + new String(buffer, 0, len1));
        System.out.println("内容长度：" + len1);*/

        /*int len2 = is.read(buffer);     //没有内容报错
        //读取多少个字节 倒出多少   offset 从桶的哪个位置开始倒   len2 读取了多少个字节
        System.out.println("内容："+new String(buffer,0, len2));
        System.out.println("内容长度："+ len2);*/

        //使用循环改进
        byte[] bytes = new byte[3];
        int len0;
        while((len0=is.read(bytes))!=-1){
            System.out.print(new String(bytes,0,len0));
        }


        //拓展；  性能比较好   缺点：无法避免汉字输出乱码的问题
    }
}
