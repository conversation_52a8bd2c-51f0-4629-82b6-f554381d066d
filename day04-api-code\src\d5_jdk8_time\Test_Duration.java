package d5_jdk8_time;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 15:45
 **/
public class Test_Duration {
    public static void main(String[] args) {
        //1.获取两个时间点之间的间隔
        LocalDateTime ld = LocalDateTime.now();
        LocalDateTime ld2 = LocalDateTime.of(2026,12,31,23,59,59);

        Duration duration = Duration.between(ld, ld2);

        //计算间隔天数   小时   分钟   毫秒
        System.out.println(duration.toDays());
        System.out.println(duration.toHours());
        System.out.println(duration.toMinutes());
        System.out.println(duration.toMillis());
    }
}