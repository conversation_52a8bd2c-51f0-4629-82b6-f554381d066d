package d11_Object;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 17:05
 **/
public class Test {
    public static void main(String[] args) {
        Student s1 = new Student("1",12,100);

        //toString 默认返回对象的地址形式,开发中输出对象更想看内容
        //因此toString 是为了让子类重写
        System.out.println(s1.toString());
        System.out.println(s1);

        //比较两个对象的地址是否一样可以采用 == 方法
        //因此 equals 的意义是让子类重写，以便自己制定
        Student t1 = new Student("师太",35,98);
        Student t2 = new Student("师太",35,98);
        System.out.println(t1.equals(t2));
        System.out.println(t1==t2);
    }
}