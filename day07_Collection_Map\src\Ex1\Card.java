package Ex1;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 19:52
 **/
//lombok 简化get set 有参 无参构造器的写法
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Card {
    private String number;
    private String color;
    private int size;

    @Override
    public String toString() {
        return  number + color;
    }
}