package d1_Stream;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class StreamTest1 {
    public static void main(String[] args) {
        // 目标：体验Stream的使用
        List<String> list = new ArrayList<>();
        list.add("张无忌");
        list.add("周芷若");
        list.add("赵敏");
        list.add("张强");
        list.add("张三丰");

        System.out.println("原始数据：");
        System.out.println(list);

        //1.拿出姓张的放到新集合中
        List<String> list1 = new ArrayList<>();
        for(String i : list){
            if(i.startsWith("张")){
                list1.add(i);
            }
        }
        System.out.println(list1);

        //2.使用Stream改进
        //   collect(Collectors.toList())  把流中的数据收集到List集合中
        // filter  过滤
        // stream 的操作： 中间操作  返回流  可以继续调用stream的方法  终止操作  返回值不再是流  不能继续调用stream的方法
        //简化集合和数组的操作结合 lambda表达式
        List<String> list2 = list.stream().filter(s -> s.startsWith("张")).collect(Collectors.toList());
        System.out.println(list2);
    }
}
