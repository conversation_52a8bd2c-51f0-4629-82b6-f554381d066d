package d1_properties;

import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.Properties;


public class PropertiesDemo3 {
    public static void main(String[] args) throws Exception {
        //创建   properties
        Properties properties = new Properties();

        properties.load(new FileReader("day11-special-file-log-code\\src\\people.txt"));

        //3.判断是否存在张三 存在将值改成18
        if(properties.containsKey("admin1")){
            properties.setProperty("admin1", "66666");
        }

        properties.store(new FileWriter("day11-special-file-log-code\\src\\people.txt"),null);
    }
}
