package d17_this;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 19:03
 **/
public class Test {
    public static void main(String[] args) {
        //目标：掌握this调用兄弟构造器
        Student student = new Student("孙悟空",500,"三星洞");

        System.out.println(student.getName());
        System.out.println(student.getAge());
        System.out.println(student.getSchoolName());

        //注意事项
        //this  super 不能同时出现，且必须在构造器的第一行
        Student student1 = new Student("蜘蛛精",17);
        System.out.println(student1.getName());
        System.out.println(student1.getAge());
        System.out.println(student1.getSchoolName());
    }
}