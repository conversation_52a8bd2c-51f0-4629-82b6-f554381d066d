package Ex1;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 19:57
 **/

public class Room {
    //准备牌
    private ArrayList<Card> allCard = new ArrayList<>();


    //2.初始化54张
    {
        String[] colors = {"♥","♠","♣","♦"};
        String[] numbers = {"3","4","5","6","7","8","9","10","J","Q","K","A","2"};
        int size = 0;
        for(String i : numbers){
            size++;
            for(String j : colors){
                allCard.add(new Card(i,j,size));
            }
        }
        allCard.add(new Card("小王","",++size));
        allCard.add(new Card("大王","",++size));
        System.out.println("新牌："+ allCard);
    }

    public void start(){
        Collections.shuffle(allCard);   //打乱
        System.out.println("洗牌："+allCard);

        //发牌
        List<Card> player1 = new ArrayList<>();
        List<Card> player2 = new ArrayList<>();
        List<Card> player3 = new ArrayList<>();

        for(int i = 0 ; i < allCard.size() -3 ; i++){
            Card card = allCard.get(i);
            if(i%3==0){
                player1.add(card);
            }else if(i%3==1){
                player2.add(card);
            }else{
                player3.add(card);
            }
        }

        sortCards(player1);
        sortCards(player2);
        sortCards(player3);

        System.out.println("玩家1："+player1);
        System.out.println("玩家2："+player2);
        System.out.println("玩家3："+player3);
        System.out.println("底牌："+allCard.subList(allCard.size()-3,allCard.size()));
    }

    public void sortCards(List<Card> cards){
        Collections.sort(cards,new  Comparator<Card>() {
            @Override
            public int compare(Card o1, Card o2) {

                return o2.getSize()-o1.getSize();
            }
        });
    }
}