package d1_char_stream;

import java.io.FileReader;
import java.io.Reader;

public class FileReaderDemo2 {
    public static void main(String[] args) throws Exception {
        try(
                Reader fr = new FileReader("day10-io-code/src/dei02.txt");
        ){
            //定义一个字符数组用来读取多个字符
            char[] buffer = new char[3];
            int len ;   //代表每次读多少个字符
            while((len = fr.read(buffer))>0){
                String rs = new String(buffer,0,len);
                System.out.print(rs);
            }

            //拓展   可以避免乱码    性能可以    这是目前学过的读取文本内容最好的方案
        }
        catch(Exception e){
            e.printStackTrace();
        }
    }
}
