package d1_byte_stream;

import java.io.FileOutputStream;

public class FileOutputStreamDemo4 {
    public static void main(String[] args) throws Exception {
        //1.创建一个文件字节输出流管道与目标文件接通
        //FileOutputStream os = new FileOutputStream("day09-io-code/src/dei04.txt");
        FileOutputStream os = new FileOutputStream("day09-io-code/src/dei04.txt",true);  //追加管理

        //2.开始写字节数据
        os.write('a');
        os.write(97);
        //os.write('徐');  //乱码只会写第一个字节

        os.write("\r\n".getBytes());

        byte[] bytes = "你是一个入门级程序员".getBytes();
        os.write(bytes);
        //public void write(byte[]  bufferr,int pos,int len)   pos 从哪个位置开始写  len 写多少个字节
        //需要计算
        os.write(bytes,21,9);

        //io流管理属于系统资源，会占用内存和相应的IO资源

        // os.flush();  //刷新缓存中的数据到磁盘文件上   关闭包含刷新
        os.close();

    }

}
