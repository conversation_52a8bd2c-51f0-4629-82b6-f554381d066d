package d1_innerclass1;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 10:38
 **/
public class People {
    private int hearBeat = 110;
    //成员内部类
    public class Heart{
        private int hearBeat = 95;
        public void show(){
            int hearBeat= 80;
            System.out.println(hearBeat);   //80
            //this 当前对象的（当前内部为的）  s
            System.out.println(this.hearBeat);   //95

            System.out.println(People.this.hearBeat);   //110
        }
    }
}