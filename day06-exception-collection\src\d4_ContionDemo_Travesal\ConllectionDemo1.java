package d4_ContionDemo_Travesal;

import java.util.ArrayList;
import java.util.Iterator;


/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 10:57
 **/
public class ConllectionDemo1 {
    public static void main(String[] args) {
        //目标：掌握collection集合的遍历方式  一 : 迭代器遍历
        //1.准备一个集合
        ArrayList<String> list = new ArrayList<>();
        list.add("A");
        list.add("B");
        list.add("C");
        System.out.println(list);

        //2.得到这个集合对象的迭代器对象  默认索引 0
        /*Iterator<String> it = list.iterator();
        while(it.hasNext()){
            String ele  = it.next();
            System.out.println(ele);
        }*/

        //增强For 遍历集合或者数组
        for (String s : list) {
            System.out.println(s);
        }

        /*System.out.println(it.next());//取完后 位置移动到下一个
        System.out.println(it.next());
        System.out.println(it.next());
        //System.out.println(it.next());  //报错没有下一个了*/


    }
}