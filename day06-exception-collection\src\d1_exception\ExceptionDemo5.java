package d1_exception;

import java.util.Scanner;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 09:43
 **/
public class ExceptionDemo5 {
    public static void main(String[] args) {
        //目标：异常的处理方式  捕获异常  尝试修复   异常一抛程序结束
        double price  = 0;
       while(true){
           try {
               price = getPrice();
           } catch (Exception e) {
               System.out.println("输入价格有问题，请重新输入");
           }
           System.out.println("本商品定价是："+price);
       }
    }

    public static double getPrice(){
        Scanner sc = new Scanner(System.in);
        System.out.println("请输入一个合法的价格");
        double price = sc.nextDouble();
        return price;
    }
}