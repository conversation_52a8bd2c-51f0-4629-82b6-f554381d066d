package d5_regex;

/**
 * @Description: 正则表达式速查表
 * @Author: Alhz
 * @Date: 2025/7/26 - 15:27
 **/
public class RegexCheatSheet {
    /*
     * ==================== 正则表达式完整速查表 ====================
     * 
     * 【基本匹配符】
     * .          匹配除换行符外的任意字符
     * ^          匹配字符串开始
     * $          匹配字符串结束
     * \          转义字符
     * |          或操作符
     * 
     * 【字符类】
     * [abc]      匹配a、b或c中的任意一个
     * [^abc]     匹配除a、b、c外的任意字符
     * [a-z]      匹配小写字母a到z
     * [A-Z]      匹配大写字母A到Z
     * [0-9]      匹配数字0到9
     * [a-zA-Z]   匹配所有字母
     * [a-zA-Z0-9] 匹配字母和数字
     * 
     * 【预定义字符类】
     * \d         匹配数字 [0-9]
     * \D         匹配非数字 [^0-9]
     * \w         匹配单词字符 [a-zA-Z0-9_]
     * \W         匹配非单词字符 [^a-zA-Z0-9_]
     * \s         匹配空白字符（空格、制表符、换行符等）
     * \S         匹配非空白字符
     * \t         匹配制表符
     * \n         匹配换行符
     * \r         匹配回车符
     * \f         匹配换页符
     * \v         匹配垂直制表符
     * 
     * 【量词】
     * *          匹配0次或多次（贪婪）
     * +          匹配1次或多次（贪婪）
     * ?          匹配0次或1次（贪婪）
     * {n}        匹配恰好n次
     * {n,}       匹配至少n次
     * {n,m}      匹配n到m次
     * *?         匹配0次或多次（非贪婪）
     * +?         匹配1次或多次（非贪婪）
     * ??         匹配0次或1次（非贪婪）
     * {n,m}?     匹配n到m次（非贪婪）
     * 
     * 【边界匹配】
     * ^          匹配行的开始
     * $          匹配行的结束
     * \b         匹配单词边界
     * \B         匹配非单词边界
     * \A         匹配字符串开始
     * \Z         匹配字符串结束
     * \z         匹配字符串结束（不包括换行符）
     * 
     * 【分组和捕获】
     * ()         捕获分组
     * (?:...)    非捕获分组
     * (?<name>...) 命名捕获分组
     * \1, \2     反向引用第1、2个捕获组
     * 
     * 【前瞻和后瞻】
     * (?=...)    正向前瞻断言
     * (?!...)    负向前瞻断言
     * (?<=...)   正向后瞻断言
     * (?<!...)   负向后瞻断言
     * 
     * 【特殊字符转义】
     * \.         匹配点号
     * \*         匹配星号
     * \+         匹配加号
     * \?         匹配问号
     * \[         匹配左方括号
     * \]         匹配右方括号
     * \{         匹配左花括号
     * \}         匹配右花括号
     * \(         匹配左圆括号
     * \)         匹配右圆括号
     * \^         匹配插入符号
     * \$         匹配美元符号
     * \|         匹配竖线
     * \\         匹配反斜杠
     * 
     * 【修饰符（Java中通过Pattern.compile的flags参数设置）】
     * Pattern.CASE_INSENSITIVE    忽略大小写
     * Pattern.MULTILINE          多行模式，^和$匹配每行的开始和结束
     * Pattern.DOTALL             点号匹配包括换行符在内的所有字符
     * Pattern.UNICODE_CASE       Unicode大小写匹配
     * Pattern.CANON_EQ           规范等价
     * Pattern.UNIX_LINES         Unix行模式
     * Pattern.LITERAL            字面量模式
     * Pattern.COMMENTS           注释模式，忽略空白和#注释
     * 
     * 【常用正则表达式模式】
     * 
     * 1. 邮箱验证
     * ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$
     * 
     * 2. 手机号验证（中国）
     * ^1[3-9]\d{9}$
     * 
     * 3. 身份证号验证（中国）
     * ^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$
     * 
     * 4. IP地址验证
     * ^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$
     * 
     * 5. URL验证
     * ^(https?|ftp)://[^\s/$.?#].[^\s]*$
     * 
     * 6. 密码强度验证（至少8位，包含大小写字母、数字和特殊字符）
     * ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$
     * 
     * 7. 中文字符匹配
     * [\u4e00-\u9fa5]
     * 
     * 8. 日期格式验证（YYYY-MM-DD）
     * ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$
     * 
     * 9. 时间格式验证（HH:MM:SS）
     * ^([01]?\d|2[0-3]):[0-5]\d:[0-5]\d$
     * 
     * 10. 银行卡号验证（16-19位数字）
     * ^\d{16,19}$
     * 
     * 11. 邮政编码验证（中国6位数字）
     * ^\d{6}$
     * 
     * 12. 车牌号验证（中国）
     * ^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$
     * 
     * 13. 十六进制颜色代码
     * ^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$
     * 
     * 14. MAC地址验证
     * ^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$
     * 
     * 15. IPv6地址验证
     * ^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$
     * 
     * 16. HTML标签匹配
     * <([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>(.*?)</\1>
     * 
     * 17. 提取HTML标签属性
     * (\w+)=["']([^"']*)["']
     * 
     * 18. 匹配双引号内的字符串
     * "([^"]*)"
     * 
     * 19. 匹配单引号内的字符串
     * '([^']*)'
     * 
     * 20. 匹配数字（包括小数）
     * ^-?\d+(\.\d+)?$
     * 
     * 【性能优化建议】
     * 1. 使用非捕获分组 (?:...) 而不是捕获分组 (...)，除非需要提取内容
     * 2. 使用字符类 [abc] 而不是选择 (a|b|c)
     * 3. 将最可能匹配的选项放在选择操作符的前面
     * 4. 避免嵌套量词，如 (a+)+
     * 5. 使用具体的量词而不是贪婪量词
     * 6. 在字符串开始处使用锚点 ^ 可以提高性能
     * 7. 预编译正则表达式模式，避免重复编译
     * 
     * 【调试技巧】
     * 1. 使用在线正则表达式测试工具
     * 2. 逐步构建复杂的正则表达式
     * 3. 使用注释模式来添加说明
     * 4. 测试边界情况和异常输入
     * 5. 考虑使用多个简单的正则表达式而不是一个复杂的
     */
    
    public static void main(String[] args) {
        System.out.println("正则表达式速查表已加载！");
        System.out.println("请查看源代码中的详细注释说明。");
    }
}
