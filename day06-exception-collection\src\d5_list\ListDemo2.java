package d5_list;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 14:18
 **/
public class ListDemo2 {
    public static void main(String[] args) {
        List<String> list  = new ArrayList<>();
        list.add("a");
        list.add("aa");
        list.add("aaa");
        list.add("aaaa");
        System.out.println("初始数组："+list);


        //1.for循环遍历
        for(int i = 0 ; i < list.size() ; i++){
            System.out.print(list.get(i)+"  ");
        }

        System.out.println();


        //2.迭代器
        Iterator it =  list.iterator();
        while(it.hasNext()){
            String ele = it.next().toString();
            System.out.print(ele+"  ");
        }

        System.out.println();

        //增强For
        for(String s: list){
            System.out.print(s+"  ");
        }

        System.out.println();

        //lambda
        list.forEach(ele -> System.out.print(ele+"  "));
    }
}