package d1_param;

import java.util.Arrays;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 16:25
 **/
public class Test {
    public static void main(String[] args) {
        //目标：可变参数使用
        //需求：求任意个整数的和
        sum();
        sum(10,20);
        sum(10,20,30);
    }
    //作用：接收数据灵活
    //可变参数在形参列表中只能出现一个，并且必须是最后一个
    public static void sum(int... a){
        //本质：可变参数在方法内部就是一个数组
        System.out.println("个数："+a.length);
        System.out.println("内容: "+ Arrays.toString(a));
        int sum1 = 0;
        for(int i : a){
            sum1 += i;
        }
        System.out.println(sum1);
    }
}