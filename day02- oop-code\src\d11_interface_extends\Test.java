package d11_interface_extends;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 16:50
 **/
public class Test {
    public static void main(String[] args) {
        //目标：接口的多继承
        //类与类 单继承  一个类只能直接继承一个父类
        //类与接口 多实现的 一个类可以实现多个接口

    }
}
//接口的多继承可以让实现类只实现一个接口，相当于实现了很多接口
class D implements A{

    @Override
    public void a() {

    }

    @Override
    public void b() {

    }

    @Override
    public void c() {

    }
}
//接口和接口是多继承的，一个接口可以同时继承多点接口
interface A extends B,C{
    void a();
}

interface B{
    void b();
}

interface C{
    void c();
}