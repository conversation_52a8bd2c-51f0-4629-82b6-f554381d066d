package d5_list;

import java.util.StringJoiner;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 14:19
 **/
public class MyLinkedList<E> {
    //定义节点类，用于创建节点对象，封闭节点数据和下一个节点对象的地址值

    private int size = 0;

    MyLinkedList.Node<E> first = null;

    public static class Node<E> {
        E item;
        Node<E> next;

        public Node(E item, Node<E> next) {
            this.item = item;
            this.next = next;
        }
    }


    public boolean add(E e){
        //维护链表
        //第一个节点，或者是后面的节点
        //创建一个节点对象，封闭这个数据
        Node<E> newnode = new Node<>(e,null);
        if(first==null){
            first= newnode;
        }
        else{
            //把这个节点加入到当前链表的最后一个节点后面
            Node<E> temp = first;
            while(temp.next!=null){
                temp = temp.next;
            }
            temp.next = newnode;
        }
        return true;
    }

    @Override
    public String toString() {
        StringJoiner sb = new StringJoiner(",","[","]");
        Node<E> temp = first;
        while(temp != null ){
            sb.add(temp.item +"");
            temp = temp.next;
        }
        return sb.toString();
    }

    public int size(){
        return size;
    }
}