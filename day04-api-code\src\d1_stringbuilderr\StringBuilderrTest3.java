package d1_stringbuilderr;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 10:18
 **/
public class StringBuilderrTest3 {
    public static void main(String[] args) {
        //使用Stringbuilder 完成对字符串的拼接操作
        int[] arr = {1, 2, 3, 4};

        System.out.println(getArrayData(arr));

    }

    public static String getArrayData(int[] arr) {
        StringBuilder sb = new StringBuilder();

        sb.append("[");
        for (int i = 0; i < arr.length; i++) {
            int data = arr[i];
            sb.append(data).append(i!=arr.length-1?",":"");
        }

        sb.append("]");
        return sb.toString();
    }
}