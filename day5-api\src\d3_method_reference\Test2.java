package d3_method_reference;

import java.util.Arrays;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 13:40
 **/
public class Test2 {
    public static void main(String[] args) {
        //实例方法引用
        //理解静态方法引用
        Student[]   students  = new Student[4];
        students[0] = new Student("张三",18,'男',1.75);
        students[1] = new Student("李四",22,'男',1.80);
        students[2] = new Student("王五",20,'男',1.85);
        students[3] = new Student("赵六",19,'男',1.90);

        Arrays.sort(students,((o1, o2) -> Double.compare((o1.getHeight()), o2.getHeight())));

        Test2 t = new Test2();
        Arrays.sort(students,(o1, o2) -> t.compare(o1,o2));
        Arrays.sort(students,t::compare);
    }

    public int compare(Student o1,Student o2){
        return Double.compare(o1.getHeight(), o2.getHeight());
    }
}