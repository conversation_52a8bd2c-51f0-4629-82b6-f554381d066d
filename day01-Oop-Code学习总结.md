# Java面向对象编程(OOP)学习总结

## 📚 学习概览

本文档总结了 `day01-Oop-Code\src` 目录下的Java面向对象编程核心概念学习内容，主要包括静态(Static)特性和继承(Inheritance)机制两大核心主题。

## 🔧 Static 静态特性

### 1. 静态变量 (d1_static_field)

**核心概念：**
- 静态变量使用 `static` 关键字修饰
- 在内存中只有一份，被类和所有对象共享
- 推荐使用类名直接访问

**完整可运行代码：d1_static_field\Student.java + Test.java**

```java
// Student.java
package d1_static_field;

public class Student {
    //静态变量： 有static 修饰， 内存中只有一份，被类和类的全部对象共享
    static String name;

    //实例变量：无static修饰，属于每个对象
    int age;
}

// Test.java
package d1_static_field;

public class Test {
    public static void main(String[] args) {
        // 1.静态变量（推荐的方式）   以最后一次修改为准
        Student.name = "张三";

        Student s1 = new Student();
        System.out.println(s1.name);

        //对象，静态变量（不推荐）
        s1.name = "马东梅";
        System.out.println(s1.name);

        Student s2 = new Student();
        s2.name = "秋雅";
        System.out.println(s2.name);

        s1.age = 18;
        s2.age = 19;
        System.out.println(s1.age);
    }
}
```

**运行结果：**
```
张三
马东梅
秋雅
18
```

**关键点：**
- 静态变量name被所有对象共享，最后一次修改影响所有访问
- 实例变量age每个对象独有，互不影响

### 2. 静态方法 (d2_static_method)

**核心概念：**
- 静态方法使用 `static` 关键字修饰
- 可以直接通过类名调用，无需创建对象
- 静态方法中不能直接访问实例变量

**完整可运行代码：d2_static_method\Student.java + test.java**

```java
// Student.java
package d2_static_method;

public class Student {
    double score;

    //静态方法: 有static 修改
    public static void printHelloWorld() {
        System.out.println("Hello World");
    }

    public void printPass() {
        System.out.println(score >= 60 ? "pass" : "no pass");
    }
}

// test.java
package d2_static_method;

public class test {
    public static void main(String[] args) {
        Student.printHelloWorld();  // 推荐：类名调用静态方法

        Student student = new Student();
        student.printHelloWorld();  // 不推荐：对象调用静态方法

        student.printPass();  // 实例方法调用

        student.score = 60;
        student.printPass();
    }
}
```

**运行结果：**
```
Hello World
Hello World
no pass
pass
```

**关键点：**
- 静态方法推荐用类名调用，不推荐用对象调用
- 实例方法必须通过对象调用
- 静态方法不能直接访问实例变量

### 3. 静态代码块 (d5_static_code)

**核心概念：**
- 使用 `static {}` 语法
- 在类加载时自动执行一次
- 用于初始化静态资源

**完整可运行代码：d5_static_code\CodeTest1.java**
```java
package d5_static_code;

import java.util.ArrayList;

public class CodeTest1 {
    //目标：搞清楚静态代码的特别，了解应用场景

    public static String schoolName = "黑马";
    public static ArrayList<String> names = new ArrayList<>();

    //静态代码块： 有static修饰，属于类持有，与类一起优先加载，自动执行一次
    static{
        System.out.println("----static静态代码执行----");

        names.add("张三");
        names.add("李四");
    }

    public static void main(String[] args){
        System.out.println(2);  // 原始代码的简单输出
    }
}
```

**运行结果：**
```
----static静态代码执行----
2
```

**关键点：**
- 静态代码块在main方法执行前就已经执行
- 用于初始化静态变量和静态资源
- 类加载时自动执行，且只执行一次

### 4. 静态工具类 (d4_static_util)

**核心概念：**
- 工具类通常私有化构造器
- 提供静态方法供外部调用
- 不需要创建对象即可使用

**完整可运行代码：d4_static_util\IteimaUtil.java + Login.java**

```java
// IteimaUtil.java
package d4_static_util;

import java.util.Random;

public class IteimaUtil {
    //工具类没有创建对象的需求，需要私有构建器
    private IteimaUtil() {
    }

    public static String createCode(int cnt) {
        String data = "ABCDEFGHIJKLMNOPQRSRUVWXYZabcdefghijklmnopqrstuvwxyz123456789";
        String code = "";
        Random random = new Random();
        for (int i = 0; i < cnt; i++) {
            int index = random.nextInt(data.length());
            code += data.charAt(index);
        }
        return code;
    }
}

// Login.java
package d4_static_util;

public class Login {
    public static void main(String[] args) {
        System.out.println(IteimaUtil.createCode(5));
    }
}
```

**运行结果（随机生成）：**
```
yXjB1
```

**关键点：**
- 工具类私有化构造器，防止实例化
- 静态方法可以直接通过类名调用
- 适用于提供通用功能的场景

### 5. 单例模式 (d6_static_singleinstanve)

**核心概念：**
- 确保类只有一个实例
- 私有构造器 + 静态变量 + 静态方法

**饿汉式单例（推荐）：**
```java
public class A {
    private static A a = new A();  // 静态变量保存唯一实例

    private A() {}  // 私有构造器

    public static A getInstance() {  // 公共访问方法
        return a;
    }
}
```

**懒汉式单例：**
```java
public class AA {
    private static AA a;

    private AA() {}

    public static AA getInstance() {
        if(a == null) {  // 第一次调用时才创建对象
            a = new AA();
        }
        return a;
    }
}
```

**运行结果：**
```
d6_static_singleinstanve.A@5aaa6d82
null
```

**关键点：**
- 饿汉式：类加载时就创建对象，线程安全
- 懒汉式：第一次使用时才创建对象，节省内存
- 通过封装确保单例的安全性

### 6. Static使用注意事项

**核心规则：**
1. **静态方法只能直接访问静态成员**
2. **实例方法可以访问所有成员（静态+实例）**
3. **静态方法中不能使用this关键字**

### 7. 代码块对比

| 特性 | 静态代码块 | 实例代码块 |
|------|------------|------------|
| 修饰符 | static {} | {} |
| 执行时机 | 类加载时执行一次 | 每次创建对象时执行 |
| 执行顺序 | 在main方法之前 | 在构造器之前 |
| 用途 | 初始化静态资源 | 初始化实例变量 |

## 🏗️ 继承 (Inheritance) 机制

### 1. 基础继承 (d7_extends, d8_extends_demo)

**核心概念：**
- 使用 `extends` 关键字实现继承
- 子类继承父类的非私有成员
- 实现代码复用和层次结构

**代码示例：**
```java
public class A {
    public int i;
    public void print1() { System.out.println(i); }
    
    private int j;  // 私有成员，子类不能继承
    private void print2() { System.out.println(j); }
}

public class B extends A {
    public void print3() {
        print1();        // 可以调用父类公共方法
        System.out.println(i);  // 可以访问父类公共变量
        // print2();     // 错误：不能访问父类私有方法
        // System.out.println(j);  // 错误：不能访问父类私有变量
    }
}
```

### 2. 访问修饰符在继承中的作用 (d9_extend_modifier, d9_extend_modifier2)

**访问权限表：**

| 修饰符 | 同类 | 同包 | 子类 | 不同包 |
|--------|------|------|------|--------|
| private | ✓ | ✗ | ✗ | ✗ |
| 默认(包访问) | ✓ | ✓ | ✗ | ✗ |
| protected | ✓ | ✓ | ✓ | ✗ |
| public | ✓ | ✓ | ✓ | ✓ |

**关键点：**
- `protected` 是为继承而设计的访问级别
- 子类可以访问父类的protected成员，但其他类不能
- 包访问权限在不同包中无效，即使是子类

### 3. 方法重写 (d12_extend_override, d13_extend_override2)

**核心概念：**
- 子类重新定义父类的方法
- 使用 `@Override` 注解确保重写正确
- 方法名、参数列表必须与父类一致

**基础重写示例：**
```java
public class Animal {
    public void run() {
        System.out.println("run");
    }
}

public class Tiger extends Animal {
    @Override
    public void run() {
        System.out.println("Tiger run");
    }
}
```



**重写规则：**
- 方法名、参数列表、返回类型必须相同
- 访问权限不能比父类更严格
- 不能重写private、static、final方法

### 4. 构造器在继承中的特点 (d15_extends_constructor, d16_extends_constructor2)

**核心概念：**
- 子类构造器会先调用父类构造器
- 默认调用父类无参构造器
- 可以使用 `super()` 显式调用父类构造器

**基础示例：**
```java
public class Animal {
    public Animal() {
        System.out.println("调用Animal无参构造器");
    }
    public Animal(String name) {
        System.out.println("调用Animal有参构造器");
    }
}

public class Wolf extends Animal {
    public Wolf() {
        super();  // 显式调用父类构造器（可省略）
        System.out.println("调用wolf的无参构造");
    }
}
```

**运行结果：**
```
调用Animal无参构造器
调用wolf的无参构造
调用Animal无参构造器
调用wolf的有参构造
```

**关键点：**
- 构造器调用顺序：先父类，后子类
- super()必须在构造器第一行
- 子类通过调用父类构造器来初始化继承的属性

### 5. this关键字的使用 (d17_this)

**核心概念：**
- `this.属性`：区分局部变量和成员变量
- `this(参数)`：调用本类其他构造器
- 必须在构造器的第一行使用

**代码示例：**
```java
public class Student {
    private String name;
    private int age;
    private String schoolName;

    public Student(String name, int age) {
        this(name, age, "黑马");  // 调用三参数构造器
    }

    public Student(String name, int age, String schoolName) {
        this.name = name;           // 区分参数和成员变量
        this.age = age;
        this.schoolName = schoolName;
    }
}
```

**测试运行：**
```java
Student student1 = new Student("蜘蛛精", 17);
System.out.println(student1.getName());        // 蜘蛛精
System.out.println(student1.getAge());         // 17
System.out.println(student1.getSchoolName());  // 黑马
```

**注意事项：**
- `this()` 和 `super()` 不能同时出现
- 都必须在构造器的第一行
- `this()` 用于构造器重载，避免代码重复

### 6. 继承中的成员访问规则

**就近原则：**
子类访问成员时遵循就近原则：局部变量 → 子类成员 → 父类成员

**关键点：**
- 使用 `this.成员` 访问当前类成员
- 使用 `super.成员` 访问父类成员
- `super` 只能在子类中使用

## 🔍 继承特性总结

### Java继承的特点
- **单继承**：Java不支持多继承，但支持多层继承
- **Object类**：所有Java类都默认继承Object类
- **代码复用**：子类自动获得父类的功能
- **层次结构**：建立清晰的类关系

## 📊 核心学习要点总结

### Static 静态特性要点
1. **内存特点**：静态成员在内存中只有一份，类级别共享
2. **加载时机**：随类加载而加载，优先于对象创建
3. **访问方式**：推荐使用类名直接访问
4. **应用场景**：工具类、单例模式、共享数据
5. **使用限制**：静态方法只能访问静态成员，不能使用this

### 继承机制要点
1. **代码复用**：子类自动获得父类的非私有成员
2. **层次结构**：建立类之间的is-a关系
3. **方法重写**：子类可以重新定义父类方法的行为
4. **构造顺序**：先调用父类构造器，再调用子类构造器
5. **访问控制**：通过修饰符控制继承的范围
6. **成员访问**：遵循就近原则（局部→子类→父类）

### 关键字使用总结
- **static**：修饰类级别的成员，实现共享和工具功能
- **extends**：实现类的继承关系
- **@Override**：确保方法重写的正确性
- **super**：访问父类成员和构造器
- **this**：访问当前类成员和构造器

### 面向对象三大特性
1. **封装**：通过访问修饰符控制成员访问权限
2. **继承**：实现代码复用和层次结构（本次学习重点）
3. **多态**：方法重写为多态提供基础（后续学习）

## 🎯 学习成果

### 核心技能掌握
- ✅ 静态特性的使用和应用场景
- ✅ 继承机制的实现原理和规范
- ✅ 访问修饰符的作用机制
- ✅ 方法重写和构造器调用规则
- ✅ this和super关键字的使用

### 后续学习方向
为后续学习多态、抽象类、接口等高级特性打下坚实基础。
