package d2_Collection;

import java.sql.ClientInfoStatus;
import java.util.*;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 10:07
 **/
public class CollectionDemo1 {
    public static void main(String[] args) {
        //单列集合 collection ： 有序、可重复、有索引
        //双列集合 map ：无序、不重复、无索引
        //1.list 系列集合 ： 添加的元素是有序、可重复、有索引
        //ArrayList linekdlist
        //2.set 系列集合 ： 添加的元素是无序、不可重复、无索引
        //hashSet LinkedHashSet  TreeSet

        //3.map 系列集合 ： 添加的元素是成对出现的 数据、键不能重复  值可以重复
        //HashMap  LinkedHashMap  TreeMap

        ArrayList<String> list = new ArrayList<>();


        // Collecttion 是单列集合的祖宗，它规定的方法是全部单列集合都得了继承的
        // add clear  remove contains isEmpty size toArray

        //添加元素
        list.add("a");
        list.add("bb");
        list.add("ccc");
        list.add("bb");

        System.out.println("数组的长度  size:"+list.size());

        System.out.println("判断是否包含某个值  contains:"+list.contains("a"));

        //从开头删除最近的一个
        System.out.println("remove 某个值："+list.remove("bb"));
        System.out.println(list);

        System.out.println("判断是否为空  isEmpty:"+list.isEmpty());


        //遍历  get  set 来修改和保存
        for(int i = 0 ; i < list.size() ; i++){
            if(list.get(i).equals("a")){
                list.remove(i);
                i--;
            }
        }
        System.out.println(list);

        //将list 转换成 String
        Object[] arrays = list.toArray(String[]::new);

        list.clear();
        System.out.println("clear后的数组："+list);
        System.out.println("判断是否为空  isEmpty:"+list.isEmpty());



        //拓展： 把别人集合的数据加给自己
        Collection<String> c1 = new ArrayList<>();
        c1.add("Java1");
        c1.add("Java2");

        Collection<String> c2 = new ArrayList<>();
        c2.add("Java1");
        c2.add("Java2");

        //将c2集合的数据全部放入c1集合
        System.out.println("c1:"+c1);
        System.out.println("c2:"+c2);
        c1.addAll(c2);
        System.out.println("放入后的"+c1);


        System.out.println();
        System.out.println("======set======");
        HashSet<String> set = new HashSet<>();
        set.add("aa");
        set.add("bb");
        set.add("ccc");
        set.add("bb");
        System.out.println(set);
    }
}