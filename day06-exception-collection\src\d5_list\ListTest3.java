package d5_list;

import java.util.ArrayList;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 14:18
 **/
public class ListTest3 {
    public static void main(String[] args) {
        //Arraylist根据索引查询速度快 : 查询数据通过地址值和索引定位，查询任意数据耗时相同
        //计算机的索引 从0开始对底层寻址速度是有帮助的
        //删除效率低 ： 可能 需要把后面数据向前移
        //添加效率低 ： 需要将元素先向后移再添加元素 或者可能需要扩容
        //总结：相对的来说 增删慢 查询快

        //利用无参构造器创建集合，会在底层创建一个默认长度为0的数组
        //当添加第一个元素的时候，会自动扩容到10
        //当添加第十一个元素的时候，会自动扩容到10*1.5=15
        //当添加第16个元素的时候，会自动扩容到15*1.5=22

        //ArrayList适合 根据索引查询数据，比如根据随机索引数据  或数据量不是很大时
        //数据量大的同时又频繁增删的场景，不建议使用ArrayList

        ArrayList<String> list = new ArrayList();

        list.add("aaa");
        list.add("bbb");
        list.add("bbb1");
        list.add("bbb2");
        list.add("bbb3");
        list.add("bbb4");
        list.add("bbb5");
        list.add("bbb6");
        list.add("bbb7");
        list.add("bbb8");
        list.add("bbb9");
        list.add("bbb10");
        list.add("bbb11");
        list.add("bbb12");
        list.add("bbb13");

        System.out.println(list.size());
    }
}