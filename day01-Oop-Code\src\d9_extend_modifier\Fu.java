package d9_extend_modifier;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 14:54
 **/
public class Fu {
    //1.private 私有的只能在本类中访问
    private void privateMethod() {
        System.out.println("privateMethod");
    }

    //2.缺省
    void  Method(){
        System.out.println("method");
    }

    //3.protect
    protected  void protectedMethod(){
        System.out.println("protectedMethod");
    }
    //4.public
    public void publicMethod(){
        System.out.println("publicMethod");
    }

    //同一个类中都可以访问
    public static void main(String[] args){
        Fu fu = new Fu();
        fu.privateMethod();
        fu.publicMethod();
        fu.protectedMethod();
        fu.Method();
    }


}