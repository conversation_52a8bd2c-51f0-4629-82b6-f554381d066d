package d5_jdk8_time;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.concurrent.atomic.LongAccumulator;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 13:46
 **/
public class LocalDateDemo {
    public static void main(String[] args) {
        //搞清楚 localDdate的常用方法

        //1.获取当前日期
        LocalDate ld = LocalDate.now();
        System.out.println(ld);

        //2.单独获取 年  月 日
        System.out.println(ld.getYear());
        System.out.println(ld.getMonthValue());
        System.out.println(ld.getDayOfMonth());

        //3.获取星期几
        System.out.println(ld.getDayOfWeek().getValue());

        //.直接修改某个信息：withYear withMonth withDayOfMonth withDayofyear
        LocalDate ld2 = ld.withYear(2099);
        LocalDate ld3 = ld.withMonth(12);

        System.out.println(ld2);
        System.out.println(ld3);

        //.把某个信息加多少： plusYears plusYears  plusDays plusWeeks
        LocalDate ld4 = ld.withDayOfMonth(1);
        LocalDate ld5 = ld.plusDays(1);
        System.out.println(ld4);
        System.out.println(ld5);

        //.把某个信息减多少：minusYears minusMonths  minusDays  minusWeeks
        LocalDate ld6 = ld.minusDays(1);
        LocalDate ld7 = ld.plusYears(1);
        System.out.println(ld6);

        //.获取指定日期的LocalDdate 对象 public static LocalDate of(int year, int month, int dayOfMonth)
        LocalDate ld8 = LocalDate.of(2099,12,31);
        System.out.println(ld8);

        //.判断两个日期是否相等
        System.out.println(ld.equals(ld8));

        //ifbefore isafter
        System.out.println(ld.isAfter(ld8));

        System.out.println(ld8.isAfter(ld));


        LocalDateTime  ld10 = LocalDateTime.now();
        System.out.println(ld10);


        //with:修改  plus:加  minus:减

    }
}