package d4_ContionDemo_Travesal;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 11:26
 **/
public class ex {
    public static void main(String[] args) throws ParseException {
        Collection<Film> film = new ArrayList<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = sdf.parse("2025-07-29 19:30:00");
        System.out.println(date);

        // 添加三部电影
        film.add(new Film("阿凡达：水之道", "2025-07-29 19:30:00", 45.0, "萨姆·沃辛顿"));
        film.add(new Film("流浪地球2", "2025-07-29 21:00:00", 42.0, "吴京"));
        film.add(new Film("速度与激情10", "2025-07-30 14:30:00", 38.0, "范·迪塞尔"));


        // 遍历显示电影信息对象    增强For
        for (Film film1 : film) {
            System.out.println(film1);
        }

    }
}