package d16_extends_constructor2;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 18:50
 **/
public class Teacher extends People{
    private String skill;

    public Teacher() {}
    public Teacher(String name,int age, String skill) {
        super(name,age);   //由父类来完成初始化内容
        this.skill = skill;
    }


    public String getSkill() {
        return skill;
    }

    public void setSkill(String skill) {
        this.skill = skill;
    }
}