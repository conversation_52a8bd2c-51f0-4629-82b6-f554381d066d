package d4_final;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 11:02
 **/
public class finalDemo1 {
    //5.final 修饰静态变量，称为常量
    //static final  修饰的成员变量叫  常量   值只有一个不能被改变
    public static final String SCHOOL_NAME = "黑马";

    public static final String SCHOOL_NAME2;
    static {
        SCHOOL_NAME2 = "黑马";
    }


    //6.final 修饰实例成员变量（没有意义）
    private final String name = "不赋值报错"; // 没有意义，不能修改



    public static void main(String[] args) {
        //目标：掌握Final关键字的作用
        //3.final 修饰变量，有且仅能赋值一次
        /*
        * 成员变量：
        *       静态成员变量   static
        *       实例成员变量
        * 局部变量 ：   方法内 形参 for循环变量，构造器中的变量
        * */

        //4.final 修饰局部变量
        final int a = 12;
        //a = 12;  //报错不支持第二次赋值



    }
}

//1.final不能被继承
//final class A{}
//class B extends A{}

//2.final修饰方法，方法不能被重写
//class C{
//    public  final void run(){
//        System.out.println("run");
//    }
//}
//class D extends C{
//    @Override
//    public void run(){
//        System.out.println("run");
//    }
//}