package d4_BigDecimalDemo1;

import org.w3c.dom.ls.LSOutput;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 11:30
 **/
public class BigDdecimalDemo1 {
    public static void main(String[] args) {
        double a = 0.1;
        double b = 0.2;
        double c = a + b;
        System.out.println(c); //0.30000000000000004

        //1.把两个数据包装成bigDdecimal
        //使用BigDecimal(String val)  解决失真问题
        BigDecimal aa = new BigDecimal(Double.toString(a));
        BigDecimal bb = new BigDecimal(Double.toString(b));

        BigDecimal cc = aa.add(bb);
        System.out.println(cc);

        //阿里巴巴 推荐使用 valueOf来包装浮点数据 成为 BigDdecimal对象
        //valueof 将 new BigDecimal(String.valueOf(val)) 封装了
        BigDecimal aaa = BigDecimal.valueOf(a);
        BigDecimal bbb = BigDecimal.valueOf(b);



        //2.调用方法进行运算
        BigDecimal ccc = aaa.add(bbb);
        System.out.println(ccc);

        //返回数据还是要用double   BigDecimal只是处理数据的手段
        double result = ccc.doubleValue();
        System.out.println(result);

        ccc = aaa.subtract(bbb);
        System.out.println(ccc);

        System.out.println("-----------------------");

        BigDecimal i = BigDecimal.valueOf(0.1);
        BigDecimal j = BigDecimal.valueOf(0.3);
        /*
        *   参数一：除数
        *   参数二：保留位数
        *   参数三：保留模式　
        * */
        BigDecimal k = i.divide(j,2, RoundingMode.HALF_EVEN);

        System.out.println(k);

    }
}