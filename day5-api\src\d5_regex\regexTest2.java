package d5_regex;

/**
 * @Description: 正则表达式完整规则说明
 * @Author: Alhz
 * @Date: 2025/7/26 - 15:27
 **/
public class regexTest2 {
    public static void main(String[] args) {
        /*
         * ==================== 正则表达式完整规则 ====================
         *
         * 1. 字符匹配
         * .          匹配任意单个字符（除换行符外）
         * [abc]      匹配字符集中的任意一个字符
         * [^abc]     匹配不在字符集中的任意字符
         * [a-z]      匹配范围内的任意字符
         * [A-Z]      匹配大写字母
         * [0-9]      匹配数字
         * [a-zA-Z]   匹配字母
         * [a-zA-Z0-9] 匹配字母和数字
         *
         * 2. 预定义字符类
         * \d         匹配数字 [0-9]
         * \D         匹配非数字 [^0-9]
         * \w         匹配单词字符 [a-zA-Z0-9_]
         * \W         匹配非单词字符 [^a-zA-Z0-9_]
         * \s         匹配空白字符（空格、制表符、换行符等）
         * \S         匹配非空白字符
         *
         * 3. 量词（Quantifiers）
         * *          匹配前面的字符0次或多次
         * +          匹配前面的字符1次或多次
         * ?          匹配前面的字符0次或1次
         * {n}        匹配前面的字符恰好n次
         * {n,}       匹配前面的字符至少n次
         * {n,m}      匹配前面的字符n到m次
         *
         * 4. 位置锚点
         * ^          匹配字符串开始位置
         * $          匹配字符串结束位置
         * \b         匹配单词边界
         * \B         匹配非单词边界
         *
         * 5. 分组和捕获
         * ()         分组，捕获匹配的内容
         * (?:...)    非捕获分组
         * |          或操作符（选择）
         *
         * 6. 转义字符
         * \\         匹配反斜杠
         * \.         匹配点号
         * \*         匹配星号
         * \+         匹配加号
         * \?         匹配问号
         * \[         匹配左方括号
         * \]         匹配右方括号
         * \{         匹配左花括号
         * \}         匹配右花括号
         * \(         匹配左圆括号
         * \)         匹配右圆括号
         * \^         匹配插入符号
         * \$         匹配美元符号
         * \|         匹配竖线
         *
         * 7. 贪婪与非贪婪匹配
         * *?         非贪婪匹配0次或多次
         * +?         非贪婪匹配1次或多次
         * ??         非贪婪匹配0次或1次
         * {n,m}?     非贪婪匹配n到m次
         *
         * 8. 常用正则表达式示例
         */

        // 邮箱验证
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

        // 手机号验证（中国）
        String phoneRegex = "^1[3-9]\\d{9}$";

        // 身份证号验证
        String idCardRegex = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[\\dXx]$";

        // IP地址验证
        String ipRegex = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";

        // 密码强度验证（至少8位，包含大小写字母、数字和特殊字符）
        String passwordRegex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$";

        // 中文字符匹配
        String chineseRegex = "[\\u4e00-\\u9fa5]";

        // 日期格式验证（YYYY-MM-DD）
        String dateRegex = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])$";

        // URL验证
        String urlRegex = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";

        // 银行卡号验证（通常16-19位数字）
        String bankCardRegex = "^\\d{16,19}$";

        // 邮政编码验证（中国6位数字）
        String postalCodeRegex = "^\\d{6}$";

        System.out.println("正则表达式规则说明已完成！");
    }
}