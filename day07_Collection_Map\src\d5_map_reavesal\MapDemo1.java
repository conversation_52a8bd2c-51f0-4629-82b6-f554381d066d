package d5_map_reavesal;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class MapDemo1 {
    public static void main(String[] args) {
        //目标：掌握map集合的遍历方式
        Map<String ,Integer> map = new HashMap<>();   //多态：一行经典代码

        map.put("java入门到跑路",2);
        map.put("华为手表",3);
        map.put("Iphone15",10);
        map.put("mate60",10);
        map.put("mate60",15);
        map.put(null,null);

        System.out.println(map);

        //取键
        Set<String> key = map.keySet();
        //根据键取值
        for(String keys:key){
            Integer value = map.get(keys);
            System.out.println(keys +  "    " + value);
        }




    }
}
