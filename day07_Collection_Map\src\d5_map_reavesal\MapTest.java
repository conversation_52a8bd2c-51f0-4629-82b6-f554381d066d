package d5_map_reavesal;

import java.util.*;

public class MapTest {
    public static void main(String[] args) {
        String[] locations = {"a","b","c","d"};


        //2.定义一个list集合 随机挑选80个学生想去的景点
        List<String> data = new ArrayList<>();
        Random random = new Random();
        for(int i = 0 ; i< 80  ; i++){
            data.add(locations[random.nextInt(locations.length)]);
        }
        System.out.println(data);

        //3.定义一个map集合  键存景点  值存 人数
        Map<String,Integer> map = new HashMap<>();

        for(String d : data){
            //判断键存在不
            if(map.containsKey(d)){
                map.put(d,map.get(d)+1);
            }
            else{
                map.put(d,1);
            }
        }

        for(Map.Entry<String,Integer> entry:map.entrySet()){
            System.out.println(entry.getKey() + "    " + entry.getValue());
        }
    }
}
