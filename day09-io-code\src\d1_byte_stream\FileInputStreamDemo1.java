package d1_byte_stream;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

public class FileInputStreamDemo1 {
    public static void main(String[] args) throws Exception {
        //目标掌握文件字节输入流每次读取一个字节的形式

        //1.创建文件的字节输入流管理与目标文件接通
        //InputStream is = new FileInputStream(new File("day09-io-code/src/dei01.txt"));  //完成写法
        InputStream is = new FileInputStream("day09-io-code/src/dei01.txt");     //简洁写法

        //2.每次读取一个字节  public int read()  如果没有字节 返回-1
        /*
        int b1 = is.read();
        System.out.println(b1);

        int b2 = is.read();
        System.out.println(b2);

        //没有字节了   返回-1
        int b3 = is.read();
        System.out.println(b3);*/

        //3.循环读取改进
        int b;   //取读一个字节
        while((b=is.read())!=-1){
            System.out.println((char)b);
        }

        //拓展    1.性能太差了一个字节一个字节读
        //2.无法避免读取汉字输出乱码的问题，会截断汉字的字节

        is.close();



        //

    }
}
