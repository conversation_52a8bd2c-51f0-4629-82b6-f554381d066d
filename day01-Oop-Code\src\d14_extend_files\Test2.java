package d14_extend_files;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/22 - 16:41
 **/
public class Test2 {
    public static void main(String[] args) {
        //目标：继承后  子类访问成员的特点，就近原则
        Zi2 zi = new Zi2();
        zi.run();
        zi.go();
    }
}

class Zi2 extends Fu2{
    @Override
    public void run() {
        System.out.println("子类run");
    }

    public void go() {
        //指定调用父类
        super.run();
    }
}
class Fu2{
    public void run(){
        System.out.println("父类run");
    }
}