package d3_method_reference;



/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 13:40
 **/

public class Student implements  Comparable<Student>{
    private String name;
    private int age;
    private char gender;
    private double height;

    public static int compareStudentByHeight(Student s1, Student s2){
        return Double.compare(s1.getHeight(),s2.getHeight());
    }

    public Student() {
    }

    public Student(String name, int age, char gender, double height) {
        this.name = name;
        this.age = age;
        this.gender = gender;
        this.height = height;
    }



    //指定大小规则
    //比较都是谁
    //比较者  this
    //被比较者  o

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public char getGender() {
        return gender;
    }

    public void setGender(char gender) {
        this.gender = gender;
    }

    public double getHeight() {
        return height;
    }

    public void setHeight(double height) {
        this.height = height;
    }
    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", gender=" + gender +
                ", height=" + height +
                '}'+'\n';
    }

    @Override
    public int compareTo(Student o) {
        return this.age - o.age;   //升序
    }
}