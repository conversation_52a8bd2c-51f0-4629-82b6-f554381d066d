package d1_properties;

import java.io.FileOutputStream;
import java.util.Properties;

public class PropertiesDemo2 {
    public static void main(String[] args) throws Exception {
        //目标：掌握使用Properties
        //1.创建属性集对象
        Properties properties = new Properties();
        properties.setProperty("admin1", "66666");
        properties.setProperty("金毛", "6611");

        System.out.println(properties);

        //2.存储到文件
        properties.store(new FileOutputStream("day11-special-file-log-code\\src\\user2.properties"), null);
    }
}
