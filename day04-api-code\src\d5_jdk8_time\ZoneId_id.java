package d5_jdk8_time;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Set;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 14:47
 **/
public class ZoneId_id {
    public static void main(String[] args) {

        //1.获取当前时区
        ZoneId zoneId = ZoneId.systemDefault();
        System.out.println(zoneId.getId());

        //获取所有时区
        Set<String> zoneIds = ZoneId.getAvailableZoneIds();
        for(String id : zoneIds){
            System.out.println(id);
        }

        ZoneId an = ZoneId.of("America/New_York");

        ZonedDateTime zdt = ZonedDateTime.now(an);

        System.out.println(zdt );

        //世界标准时间：  服务器获取世界时间
        ZonedDateTime utc = ZonedDateTime.now(ZoneId.of("UTC"));
        System.out.println(utc);
    }


}