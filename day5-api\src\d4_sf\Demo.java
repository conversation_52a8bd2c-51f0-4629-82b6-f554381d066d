package d4_sf;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 15:16
 **/
public class Demo {
    public static void main(String[] args) {
        //目标: 完成排序
        //1.
        int[] a = {5,2,3,1};

       /* for(int i = 0 ; i < a.length - 1 ; i++){
            for(int j =0 ; j < a.length - 1 - i ; j++){
                if(a[j]>a[j+1]){
                    int temp = a[j];
                    a[j] = a[j+1];
                    a[j+1] = temp;
                }
            }
        }*/

        for(int i = 0 ; i < a.length - 1 ; i++){
            int tmp = a[i];
            for(int j = i +1 ;j< a.length ; j++){
                if(a[j]<tmp){
                    a[i] = a[j];
                    a[j] = tmp;
                }
            }
        }

    }
}