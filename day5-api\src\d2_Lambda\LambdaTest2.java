package d2_Lambda;

import d1_array.Student;

import java.util.Arrays;
import java.util.Comparator;
import java.util.function.IntToDoubleFunction;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/26 - 10:56
 **/
public class LambdaTest2 {

    public static void main(String[] args) {

        double[] score={99.5,90,59.5,78,98,55};
        //需求给每个分数都加10分
        Arrays.setAll(score, index -> {
            return score[index]+10;
        });

        //省略
        Arrays.setAll(score, index -> score[index]+10);


        System.out.println(Arrays.toString(score));


        System.out.println("======================================");

        Student[]   students  = new Student[4];
        students[0] = new Student("张三",18,'男',1.75);
        students[1] = new Student("李四",22,'男',1.80);
        students[2] = new Student("王五",20,'男',1.85);
        students[3] = new Student("赵六",19,'男',1.90);

        Arrays.sort(students, new Comparator<Student>() {
            @Override
            public int compare(Student o1, Student o2) {
                //需要注意数据的类型
                if(o1.getAge() == o2.getAge()){
                    return 0;
                }
                else if (o1.getAge() > o2.getAge()){
                    return 1;
                }
                return -1;
                //return o2.getAge() - o1.getAge();
                //return Double.compare(o1.getHeight(), o2.getHeight());    比较double类型的数据需要注意   不可以用强制类型转换
            }
        });


        Arrays.sort(students, (Student o1, Student o2) ->{
                //需要注意数据的类型
                if(o1.getAge() == o2.getAge()){
                    return 0;
                }
                else if (o1.getAge() > o2.getAge()){
                    return 1;
                }
                return -1;
        });

        Arrays.sort(students, ( o1,  o2) ->{
            //需要注意数据的类型
            if(o1.getAge() == o2.getAge()){
                return 0;
            }
            else if (o1.getAge() > o2.getAge()){
                return 1;
            }
            return -1;
        });

        Arrays.sort(students, ( o1,  o2) ->  o1.getAge() - o2.getAge());

        System.out.println(Arrays.toString(students));
    }
}