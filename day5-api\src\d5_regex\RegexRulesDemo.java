package d5_regex;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * @Description: 正则表达式规则详细演示
 * @Author: Alhz
 * @Date: 2025/7/26 - 15:27
 **/
public class RegexRulesDemo {
    public static void main(String[] args) {
        System.out.println("=============== 正则表达式完整规则演示 ===============\n");
        
        // 1. 基本字符匹配
        demonstrateBasicMatching();
        
        // 2. 字符类演示
        demonstrateCharacterClasses();
        
        // 3. 量词演示
        demonstrateQuantifiers();
        
        // 4. 位置锚点演示
        demonstrateAnchors();
        
        // 5. 分组和选择演示
        demonstrateGroupsAndAlternation();
        
        // 6. 实际应用示例
        demonstratePracticalExamples();
    }
    
    private static void demonstrateBasicMatching() {
        System.out.println("1. 基本字符匹配:");
        
        // 点号匹配任意字符
        testRegex("a.c", "abc", "adc", "a1c", "a c");
        
        // 字符集匹配
        testRegex("[abc]", "a", "b", "c", "d");
        
        // 否定字符集
        testRegex("[^abc]", "a", "d", "1", "!");
        
        // 范围匹配
        testRegex("[a-z]", "a", "m", "z", "A", "1");
        
        System.out.println();
    }
    
    private static void demonstrateCharacterClasses() {
        System.out.println("2. 预定义字符类:");
        
        // 数字匹配
        testRegex("\\d", "1", "a", "9", "!");
        
        // 单词字符匹配
        testRegex("\\w", "a", "1", "_", "!");
        
        // 空白字符匹配
        testRegex("\\s", " ", "\t", "a", "1");
        
        System.out.println();
    }
    
    private static void demonstrateQuantifiers() {
        System.out.println("3. 量词演示:");
        
        // * 匹配0次或多次
        testRegex("ab*c", "ac", "abc", "abbc", "abbbc");
        
        // + 匹配1次或多次
        testRegex("ab+c", "ac", "abc", "abbc", "abbbc");
        
        // ? 匹配0次或1次
        testRegex("ab?c", "ac", "abc", "abbc");
        
        // {n} 匹配恰好n次
        testRegex("ab{2}c", "ac", "abc", "abbc", "abbbc");
        
        // {n,m} 匹配n到m次
        testRegex("ab{1,3}c", "ac", "abc", "abbc", "abbbc", "abbbbc");
        
        System.out.println();
    }
    
    private static void demonstrateAnchors() {
        System.out.println("4. 位置锚点:");
        
        // ^ 开始位置
        testRegex("^abc", "abc", "abcdef", "xabc");
        
        // $ 结束位置
        testRegex("abc$", "abc", "xabc", "abcx");
        
        // \b 单词边界
        testRegex("\\bcat\\b", "cat", "catch", "tomcat", "cat dog");
        
        System.out.println();
    }
    
    private static void demonstrateGroupsAndAlternation() {
        System.out.println("5. 分组和选择:");
        
        // | 选择操作符
        testRegex("cat|dog", "cat", "dog", "bird");
        
        // () 分组
        testRegex("(ab)+", "ab", "abab", "ababab", "a");
        
        System.out.println();
    }
    
    private static void demonstratePracticalExamples() {
        System.out.println("6. 实际应用示例:");
        
        // 邮箱验证
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        System.out.println("邮箱验证:");
        testRegex(emailRegex, "<EMAIL>", "<EMAIL>", "invalid-email", "user@");
        
        // 手机号验证
        String phoneRegex = "^1[3-9]\\d{9}$";
        System.out.println("手机号验证:");
        testRegex(phoneRegex, "13812345678", "15987654321", "12345678901", "1381234567");
        
        // 密码强度验证
        String passwordRegex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$";
        System.out.println("密码强度验证:");
        testRegex(passwordRegex, "Password123!", "password123", "PASSWORD123!", "Pass123!");
        
        System.out.println();
    }
    
    private static void testRegex(String regex, String... testStrings) {
        Pattern pattern = Pattern.compile(regex);
        System.out.printf("正则: %-20s ", regex);
        
        for (String test : testStrings) {
            Matcher matcher = pattern.matcher(test);
            boolean matches = matcher.matches();
            System.out.printf("'%s':%s ", test, matches ? "✓" : "✗");
        }
        System.out.println();
    }
}
