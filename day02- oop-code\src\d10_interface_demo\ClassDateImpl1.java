package d10_interface_demo;

import java.util.ArrayList;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 16:13
 **/
public class ClassDateImpl1 implements ClassDate {
    private ArrayList<Student> students;

    public ClassDateImpl1(ArrayList<Student> students) {
        this.students = students;
    }

    /*
    * 第一套实现类
    * */
    @Override
    public void printAllStudentInfo() {
        System.out.println("=== 输出所有学生信息 ===");
        for(int i=0;i<students.size();i++){
            System.out.println(students.get(i).toString());
        }
    }

    @Override
    public void printAllStudentAverageScore() {
        System.out.println("=== 输出所有学生平均成绩 ===");
        double sum=0;
        for(int i=0;i<students.size();i++){
            sum+=students.get(i).getScore();
        }
        double avg=sum/students.size();
        System.out.println(avg);
    }
}