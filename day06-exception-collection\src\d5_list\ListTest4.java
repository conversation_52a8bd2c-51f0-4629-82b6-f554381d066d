package d5_list;

import java.util.LinkedList;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 14:19
 **/
public class ListTest4 {
    public static void main(String[] args) {
        //链表  ： 查询慢，每次都要坐着开始找
        //增删快  ：  直接找到前一个就可以  修改地址指向就行了
        //双链表 ： 查询慢，增删相对快，但对首尾元素进行CRUD速度快

        //addFirst  addLast  getFirst  getLast  removeFirst removevLast

        LinkedList<String> queue = new LinkedList<>();

        //入队  队列
        queue.addLast("a排队");
        queue.addLast("b排队");
        queue.addLast("c排队");
        queue.addLast("d排队");
        queue.addLast("e排队");
        System.out.println(queue);

        //出队
        System.out.println(queue.removeFirst());
        System.out.println(queue.removeFirst());
        System.out.println(queue.removeFirst());
        System.out.println(queue);


        System.out.println("===栈===");
        LinkedList<String>  stack = new LinkedList<>();
        // push == addFirst    pop == removeFirst
        stack.push("a入栈");
        stack.push("b入栈");
        stack.push("c入栈");
        stack.push("d入栈");
        stack.push("e入栈");
        System.out.println(stack.pop());
        System.out.println(stack.pop());
        System.out.println(stack.pop());
        System.out.println(stack.pop());

    }
}