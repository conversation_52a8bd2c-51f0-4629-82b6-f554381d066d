package d1_static_field;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/21 - 18:22
 **/
public class Test {
    public static void main(String[] args) {
        // 1.静态变量（推荐的方式）   以最后一次修改为准
        Student.name = "张三";

        //
        Student s1 = new Student();

        System.out.println(s1.name);

        //对象，静态变量（不推荐）
        s1.name = "马东梅";
        System.out.println(s1.name);

        Student s2 = new Student();
        s2.name ="秋雅";
        System.out.println(s2.name);

        s1.age = 18;
        s2.age = 19;
        System.out.println(s1.age);
    }
}