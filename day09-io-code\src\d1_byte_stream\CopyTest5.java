package d1_byte_stream;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

public class CopyTest5 {
    public static void main(String[] args) {
        //字节流适合做一切文件的复制操作
        try {
            //创建字节输入流管道与源文件接通
            InputStream is = new FileInputStream("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01.png");

            //创建一个字节输出流管道与目标文件接通
            OutputStream os = new FileOutputStream("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01_copy.png");

            //
            byte[] buffer = new byte[1024];  //1KB

            int len;
            while((len=is.read(buffer))!=-1){
                os.write(buffer,0,len);
            }

            os.close();
            is.close();

            System.out.println("复制成功");

        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
