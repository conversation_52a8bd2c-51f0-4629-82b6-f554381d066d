package d4_innerclass4_2;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 14:38
 **/
public class Test3 {
    public static void main(String[] args) {
        //掌握匿名内部类的真实场景

        //GUI SWING编程，桌面编程
        //1.创建一个窗口
        JFrame win = new JFrame("登录一下");

        JPanel panel = new JPanel();
        win.add(panel);

        JButton btn = new JButton("登录");
        panel.add(btn);


        //给按钮绑定单击事件监听器对象，可以用来监听用户的点击，以便做出对应的内容
        //匿名内部类是作为一个对象 参数传输给方法使用，至于什么时候用，只有方法强制才需要使用
        //最重要的作用： 简化代码，新技术的基础
        btn.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                System.out.println("点击我");
                JOptionPane.showMessageDialog(win,"别来");
            }
        });


        win.setSize(400,300);
        win.setLocationRelativeTo(null);
        win.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        win.setVisible(true);

    }
}