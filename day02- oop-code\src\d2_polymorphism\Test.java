package d2_polymorphism;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 09:51
 **/
public class Test {
    public static void main(String[] args) {
        //目标:搞清楚使用多态的好处
        //1.多态下右边对象是解耦合的。

        Animals a = new Cat();
        go(a);



        a = new Dog();
        go(a);
    }

    //2.多态下，父类类型作为方法的形参，可以接收一切子类对象，方法更通用
    public static void go(Animals a){
        System.out.println("开始");
        a.cry();   //对象回调
        System.out.println("结束");
    }
}