package d5_abstract;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 13:40
 **/


//抽象方法：使用abstract来修饰
public abstract class A {
    //抽象方法使用 abstract 来修饰，只能有方法签名没有方法体

    private String name;
    private int age;

    public A(){
    }

    public A(String name,int age){
        this.name=name;
        this.age=age;
    }

    public abstract void go();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }
}