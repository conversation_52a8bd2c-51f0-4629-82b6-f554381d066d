package d1_byte_stream;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

public class FileInputStreamDemo3 {
    public static void main(String[] args) throws Exception {
        InputStream is = new FileInputStream("day09-io-code/src/dei03.txt");     //简洁写法
        //2.定义一个字节数组与被读取的源文件一样大
        File f = new File("day09-io-code/src/dei03.txt");
        //根据文件大小定义桶的大小  一次性读取完所有内容
        byte[] buffer = new byte[(int)f.length()];


        int len = is.read(buffer);

        System.out.println("读取到的字节数：" + len);

        System.out.println(new String(buffer));


        // reaaAllBytes   jdk9可以使用
        //只能读取小文件
        byte[] bytes = is.readAllBytes();
        System.out.println(new String(bytes));
    }
}
