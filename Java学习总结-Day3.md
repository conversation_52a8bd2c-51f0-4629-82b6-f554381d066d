# Java学习总结 - Day3: 抽象类与接口编程

## 📌 当日核心学习目标
- 深入理解抽象类的设计思想和应用场景
- 掌握接口的定义和实现方法
- 熟练运用JDK8接口新特性（默认方法、静态方法、私有方法）
- 理解抽象类与接口的区别和选择原则
- 掌握模板方法模式的实现

## 🎯 主要知识点详解

### 1. 抽象类 (Abstract Classes)

#### 1.1 抽象类基础概念
**核心定义：**
- 使用`abstract`关键字修饰的类
- 不能被实例化，只能被继承
- 可以包含抽象方法和具体方法
- 为子类提供通用的模板和规范

**语法格式：**
```java
public abstract class Shape {
    protected String color;

    // 构造器
    public Shape(String color) {
        this.color = color;
    }

    // 具体方法
    public void setColor(String color) {
        this.color = color;
    }

    // 抽象方法：子类必须实现
    public abstract double calculateArea();
    public abstract double calculatePerimeter();

    // 模板方法：定义算法骨架
    public final void printInfo() {
        System.out.println("颜色：" + color);
        System.out.println("面积：" + calculateArea());
        System.out.println("周长：" + calculatePerimeter());
    }
}
```

#### 1.2 抽象类的实现
**具体子类实现：**
```java
public class Circle extends Shape {
    private double radius;

    public Circle(String color, double radius) {
        super(color);
        this.radius = radius;
    }

    @Override
    public double calculateArea() {
        return Math.PI * radius * radius;
    }

    @Override
    public double calculatePerimeter() {
        return 2 * Math.PI * radius;
    }

    // 子类特有方法
    public double getRadius() {
        return radius;
    }
}

public class Rectangle extends Shape {
    private double width, height;

    public Rectangle(String color, double width, double height) {
        super(color);
        this.width = width;
        this.height = height;
    }

    @Override
    public double calculateArea() {
        return width * height;
    }

    @Override
    public double calculatePerimeter() {
        return 2 * (width + height);
    }
}
```

#### 1.3 模板方法模式
**核心思想：**
- 在抽象类中定义算法的骨架
- 将具体步骤延迟到子类中实现
- 实现代码复用和统一流程控制

**实际应用示例：**
```java
public abstract class DataProcessor {
    // 模板方法：定义数据处理流程
    public final void processData() {
        loadData();
        validateData();
        transformData();
        saveData();
        System.out.println("数据处理完成");
    }

    // 具体方法：通用的数据加载
    private void loadData() {
        System.out.println("加载数据");
    }

    // 抽象方法：子类实现具体的处理步骤
    protected abstract void validateData();
    protected abstract void transformData();
    protected abstract void saveData();
}

// 具体实现类
public class XMLDataProcessor extends DataProcessor {
    @Override
    protected void validateData() {
        System.out.println("验证XML数据格式");
    }

    @Override
    protected void transformData() {
        System.out.println("转换XML数据");
    }

    @Override
    protected void saveData() {
        System.out.println("保存到XML文件");
    }
}
```

### 2. 接口编程 (Interface Programming)

#### 2.1 接口基础概念
**核心定义：**
- 使用`interface`关键字定义
- 是一种引用数据类型，类似于类
- 定义了一组抽象方法的集合
- 实现类必须实现接口中的所有抽象方法

**JDK8之前的接口特点：**
```java
public interface Animal {
    // 常量：public static final（可省略）
    String KINGDOM = "动物界";
    int MAX_AGE = 200;

    // 抽象方法：public abstract（可省略）
    void eat();
    void sleep();
    void move();
}
```

#### 2.2 接口的实现
**单接口实现：**
```java
public class Dog implements Animal {
    private String name;

    public Dog(String name) {
        this.name = name;
    }

    @Override
    public void eat() {
        System.out.println(name + "正在吃狗粮");
    }

    @Override
    public void sleep() {
        System.out.println(name + "正在睡觉");
    }

    @Override
    public void move() {
        System.out.println(name + "正在跑步");
    }
}
```

**多接口实现：**
```java
public interface Flyable {
    void fly();
}

public interface Swimmable {
    void swim();
}

public class Duck implements Animal, Flyable, Swimmable {
    private String name;

    public Duck(String name) {
        this.name = name;
    }

    @Override
    public void eat() {
        System.out.println(name + "正在吃鱼虾");
    }

    @Override
    public void sleep() {
        System.out.println(name + "正在水边休息");
    }

    @Override
    public void move() {
        System.out.println(name + "正在游泳");
    }

    @Override
    public void fly() {
        System.out.println(name + "正在飞翔");
    }

    @Override
    public void swim() {
        System.out.println(name + "正在游泳");
    }
}
```

### 3. JDK8接口新特性（基于实际项目代码）

#### 3.1 默认方法、静态方法和私有方法
**实际项目代码（A.java）：**
```java
public interface A {
    // 默认方法  实例方法  必需用default修饰
    // 默认用public 修饰
    public default void run(){
        go();
        System.out.println("run");
    }

    // 私有方法(私有的实例方法)  jdk9才有的
    // 只能当前接口内部的默认方法或者私有方法来调用
    private void go(){
        System.out.println("go go go");
    }

    // 3.静态方法
    // 默认会用public修饰
    // 接口的静态方法必须用接口名本身调用
    static void inAddr(){
        System.out.println("inAddr go go");
    }
}
```

#### 3.2 接口实现类
**实际项目代码（B.java）：**
```java
public class B implements A {
    @Override
    public void run() {
        System.out.println("B类重写了默认方法");
    }
}
```

#### 3.3 JDK8接口特性测试
**实际应用测试：**
```java
public class InterfaceTest {
    public static void main(String[] args) {
        // 创建实现类对象
        B b = new B();
        
        // 调用重写的默认方法
        b.run();
        
        // 调用接口的静态方法（必须用接口名调用）
        A.inAddr();
        
        // 创建匿名实现类测试默认方法
        A a = new A() {};
        a.run(); // 调用默认方法，内部会调用私有方法go()
    }
}
```

#### 3.4 JDK8接口新特性详解
**默认方法 (Default Methods)：**
- 使用`default`关键字修饰
- 有方法体，实现类可以选择重写
- 解决接口演化问题，保持向后兼容

**静态方法 (Static Methods)：**
- 属于接口，通过接口名调用
- 不能被实现类重写
- 提供工具方法的便捷方式

**私有方法 (Private Methods, JDK9+)：**
- 为默认方法和静态方法提供辅助
- 避免代码重复，提高代码复用性
- 只能在接口内部使用

### 4. 接口继承
**接口间的继承关系：**
```java
public interface Vehicle {
    void start();
    void stop();
}

public interface Car extends Vehicle {
    void drive();
    
    // 可以重新声明父接口的方法
    @Override
    void start();
}

public interface ElectricCar extends Car {
    void charge();
    
    // 默认方法
    default void displayBatteryLevel() {
        System.out.println("电池电量：80%");
    }
}

// 实现类
public class Tesla implements ElectricCar {
    @Override
    public void start() {
        System.out.println("特斯拉启动");
    }

    @Override
    public void stop() {
        System.out.println("特斯拉停止");
    }

    @Override
    public void drive() {
        System.out.println("特斯拉行驶");
    }

    @Override
    public void charge() {
        System.out.println("特斯拉充电");
    }
}
```

## 💻 实际代码示例

### 设备管理系统
```java
// 设备接口
public interface Device {
    String MANUFACTURER = "科技公司";  // 常量
    
    void powerOn();
    void powerOff();
    
    // JDK8默认方法
    default void restart() {
        powerOff();
        System.out.println("等待3秒...");
        powerOn();
    }
    
    // JDK8静态方法
    static void printManufacturer() {
        System.out.println("制造商：" + MANUFACTURER);
    }
}

// 网络设备接口
public interface NetworkDevice extends Device {
    void connect();
    void disconnect();
    
    default void checkConnection() {
        System.out.println("检查网络连接状态");
    }
}

// 路由器实现
public class Router implements NetworkDevice {
    private String model;
    
    public Router(String model) {
        this.model = model;
    }
    
    @Override
    public void powerOn() {
        System.out.println(model + "路由器开机");
    }
    
    @Override
    public void powerOff() {
        System.out.println(model + "路由器关机");
    }
    
    @Override
    public void connect() {
        System.out.println(model + "连接到网络");
    }
    
    @Override
    public void disconnect() {
        System.out.println(model + "断开网络连接");
    }
    
    // 重写默认方法
    @Override
    public void restart() {
        System.out.println("路由器重启中...");
        NetworkDevice.super.restart();  // 调用接口的默认方法
    }
}

// 测试类
public class DeviceTest {
    public static void main(String[] args) {
        Router router = new Router("TP-Link");
        
        // 调用实现的方法
        router.powerOn();
        router.connect();
        
        // 调用默认方法
        router.checkConnection();
        router.restart();
        
        // 调用静态方法
        Device.printManufacturer();
        
        router.disconnect();
        router.powerOff();
    }
}
```

## 🔍 重点难点分析

### 1. 抽象类 vs 接口的选择
**抽象类适用场景：**
- 有共同的代码实现需要复用
- 需要定义非public的方法
- 需要定义实例变量
- 类之间有明确的is-a关系

**接口适用场景：**
- 定义规范和契约
- 支持多重继承
- 不同类之间的共同行为
- 面向接口编程

### 2. JDK8接口新特性的意义
- **向后兼容**：默认方法解决接口演化问题
- **代码复用**：避免在每个实现类中重复代码
- **工具方法**：静态方法提供便捷的工具功能

### 3. 接口设计原则
- **单一职责**：每个接口应该有明确的职责
- **接口隔离**：客户端不应该依赖它不需要的接口
- **依赖倒置**：高层模块不应该依赖低层模块

## 📝 当日学习总结和要点回顾

### 🎯 抽象类要点
1. **设计目的**：为子类提供通用模板和规范
2. **组成元素**：抽象方法 + 具体方法 + 构造器 + 成员变量
3. **继承规则**：子类必须实现所有抽象方法
4. **应用模式**：模板方法模式，定义算法骨架
5. **使用场景**：有共同代码需要复用的继承关系

### 🎯 接口编程要点
1. **核心作用**：定义规范和契约，实现多重继承
2. **JDK8新特性**：默认方法、静态方法、私有方法
3. **实现规则**：实现类必须实现所有抽象方法
4. **继承关系**：接口可以继承接口，类可以实现多个接口
5. **设计原则**：面向接口编程，依赖抽象而非具体

### 🎯 抽象类与接口对比
| 特性 | 抽象类 | 接口 |
|------|--------|------|
| 关键字 | abstract class | interface |
| 继承 | 单继承 | 多实现 |
| 方法 | 抽象+具体 | 抽象+默认+静态 |
| 变量 | 实例+静态 | 常量(public static final) |
| 构造器 | 可以有 | 不能有 |
| 访问修饰符 | 任意 | public |

### 🎯 设计模式应用
- **模板方法模式**：抽象类定义算法骨架
- **策略模式**：接口定义策略规范
- **适配器模式**：接口实现类型转换
- **工厂模式**：接口作为产品类型

---

**下一步学习预告：** Day4将学习内部类、泛型与枚举，深入理解Java的高级特性和类型系统。
