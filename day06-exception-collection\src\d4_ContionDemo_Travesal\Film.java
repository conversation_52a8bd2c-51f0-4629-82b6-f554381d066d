package d4_ContionDemo_Travesal;

import java.util.Date;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 11:28
 **/
public class Film {
    private String name;
    private String  startTime;
    private double price;
    private String actor;

    public Film() {
    }
    public Film(String name, String startTime, double price, String actor){
        this.name = name;
        this.startTime = startTime;
        this.price = price;
        this.actor = actor;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getActor() {
        return actor;
    }

    public void setActor(String actor) {
        this.actor = actor;
    }

    @Override
    public String toString() {
        return "Film{" +
                "name='" + name + '\'' +
                ", startTime='" + startTime + '\'' +
                ", price=" + price +
                ", actor='" + actor + '\'' +
                '}';
    }
}