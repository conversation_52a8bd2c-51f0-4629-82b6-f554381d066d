package d2_finally;

import java.io.*;

public class FinallyDemo3 {
    public static void main(String[] args) {


        //字节流适合做一切文件的复制操作
        try(
                //用完之后自动释放资源对象   用完后会自动调用资源的close 方法关闭资源
                //创建字节输入流管道与源文件接通
                InputStream is = new FileInputStream("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01.png");
                //创建一个字节输出流管道与目标文件接通
                OutputStream os = new FileOutputStream("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01_copy.png");
        ) {

            byte[] buffer = new byte[1024];  //1KB

            int len;
            while((len=is.read(buffer))!=-1){
                os.write(buffer,0,len);
            }

        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
