package d2_File;

import java.io.File;

public class FileDemo1 {
    public static void main(String[] args) {
        //创建file对象
        File file1 = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01.png");
        /*File file2 = new File("D:/Code/ST-Java/Java-01/JavaSEProMax/Resource/image_01.png");

        //"D:\Code\ST-Java\Java-01\JavaSEProMax\Resource\image_01.png"
        File file3 = new File("D:"+File.separator+"Code"+File.separator+"ST-Java"+File.separator+"Java-01"+File.separator+"JavaSEProMax"+File.separator+"Resource"+File.separator+"image_01.png");
        */

        //1.获取图片字节个数
        System.out.println(file1.length());

        //2.file对象可以代表文件，也可以代表文件夹
        File file2 = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource");
        //文件夹拿到的是文件夹本身的大小，不是里面全部内容的大小
        System.out.println(file2.length());

        //3.file对象代表的文件路径可以是不存在的
        File file3 = new File("E:\\Code");


        //4.File对象的路径可以使用相对路径
        //相对地址与绝对地址 相对路径在工程下找

        //一般用来找项目中的资源
        File file4 = new File("JavaSEProMax/Resource/image_01.png");

        System.out.println(file4);

    }
}
