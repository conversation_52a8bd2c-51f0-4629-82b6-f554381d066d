package d1_stringbuilderr;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 09:51
 **/
public class StringBuilderDemo2 {
    public static void main(String[] args) {
        //目标：进行Stringbuilder拼接的性能
        //1.使用String 拼接看看效率
        //拼接的效率慢，性能低
//        String s = "";
//        for(int i=0;i<1000000;i++){
//            s+="abc";
//        }
//        System.out.println(s);


        //字符串拼接的操作用stringbuilder
        //StringBuilder线程不安全   Stringbuffer线程安全
        StringBuilder s = new StringBuilder();
        for(int i=0;i<1000000;i++){
            s.append("abc");
        }
        System.out.println(s);
    }
}