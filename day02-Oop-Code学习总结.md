# Java面向对象编程进阶学习总结

## 📚 学习概览

本文档总结了 `day02-oop-code\src` 目录下的Java面向对象编程进阶概念学习内容，主要包括多态(Polymorphism)机制、Final关键字、抽象类(Abstract)、接口(Interface)编程和设计模式等核心主题。

## 🔄 多态 (Polymorphism) 机制

### 1. 多态基础概念 (d1_polymorphism)

**核心概念：**
- 多态是同一个方法在不同对象上产生不同的行为
- 使用父类类型的引用指向子类对象
- 体现面向对象编程的灵活性

**完整可运行代码：d1_polymorphism\Animals.java + Cat.java + Dog.java + Test.java**
```java
// Animals.java
package d1_polymorphism;

public class Animals {
    public void cry(){
        System.out.println("动物叫");
    }
}

// Cat.java
package d1_polymorphism;

public class Cat extends Animals{
    @Override
    public void cry() {
        System.out.println("猫咪叫");
    }
}

// Dog.java
package d1_polymorphism;

public class Dog extends Animals{
    @Override
    public void cry() {
        System.out.println("狗汪叫");
    }
}

// Test.java
package d1_polymorphism;

public class Test {
    public static void main(String[] args) {
        // 多态的形式：父类类型 对象名称 = new 子类构造器;
        Animals a1 = new Cat();
        a1.cry();  // 猫咪叫

        Animals a2 = new Dog();
        a2.cry();  // 狗汪叫
    }
}
```

**运行结果：**
```
猫咪叫
狗汪叫
```

**关键点：**
- 父类引用指向子类对象，调用重写方法时执行子类的实现
- 体现"编译看左边，运行看右边"的特性

### 2. 多态的使用前提和好处 (d2_polymorphism)

**使用前提：**
1. 必须有继承关系
2. 必须有父类引用指向子类对象
3. 必须有方法重写

**完整可运行代码：d2_polymorphism\Test.java**
```java
package d2_polymorphism;

public class Test {
    public static void main(String[] args) {
        //1.多态下右边对象是解耦合的。
        Animals a1 = new Cat();
        a1.cry();

        Animals a2 = new Dog();
        a2.cry();

        //2.多态下，父类类型作为方法的形参，可以接收一切子类对象，方法更通用
        go(a1);
        go(a2);
    }

    public static void go(Animals a){
        a.cry();   // 对象回调，体现多态
    }
}
```

**运行结果：**
```
猫咪叫
狗汪叫
猫咪叫
狗汪叫
```

**多态的好处：**
1. **解耦合**：右边对象可以灵活替换，便于扩展与维护
2. **扩展性强**：父类类型作为方法形参，可以接收一切子类对象
3. **代码复用**：一个方法可以处理多种子类对象

### 3. 多态下的类型转换 (d3_polymorphism)

**核心概念：**
- 向上转型：自动进行，安全
- 向下转型：强制转换，需要注意类型安全
- instanceof关键字：判断对象真实类型

**完整可运行代码：d3_polymorphism\Test.java**
```java
package d3_polymorphism;

public class Test {
    public static void main(String[] args) {
        // 自动类型转换（向上转型）
        Animals a1 = new Dog();
        a1.cry();

        // 强制类型转换（向下转型）
        if(a1 instanceof Dog){
            Dog d1 = (Dog) a1;
            d1.lookDoor();  // 调用子类独有方法
        }

        if(a1 instanceof Cat){
            Cat c1 = (Cat) a1;
            c1.catchMouse();
        } else {
            System.out.println("不是Cat类型，无法转换");
        }
    }
}
```

**运行结果：**
```
狗汪叫
狗看门
不是Cat类型，无法转换
```

**关键点：**
- 使用instanceof判断类型后再进行强制转换，避免ClassCastException
- 向下转型的目的是调用子类独有的方法
- 类型转换遵循继承关系

## 🔒 Final 关键字

### 1. Final修饰类和方法 (d4_final)

**核心概念：**
- final修饰类：该类不能被继承
- final修饰方法：该方法不能被重写
- final限制了继承和多态的使用

**完整可运行代码：d4_final\finalDemo1.java**
```java
package d4_final;

// final修饰类，不能被继承
final class A {
    // final修饰方法，不能被重写
    public final void test() {
        System.out.println("final方法");
    }
}

// class B extends A {}  // 编译错误：不能继承final类

class C {
    public final void show() {
        System.out.println("C的final方法");
    }
}

class D extends C {
    // public void show() {}  // 编译错误：不能重写final方法
}

public class finalDemo1 {
    public static void main(String[] args) {
        A a = new A();
        a.test();

        C c = new C();
        c.show();
    }
}
```

**运行结果：**
```
final方法
C的final方法
```

### 2. Final修饰变量 (d4_final)

**核心概念：**
- final修饰变量：该变量只能赋值一次，成为常量
- 基本类型：数据值不能改变
- 引用类型：地址不能改变，但对象内容可以改变

**完整可运行代码：d4_final\finalDemo2.java**
```java
package d4_final;

import java.util.ArrayList;

public class finalDemo2 {
    // 常量定义
    public static final String SCHOOL_NAME = "黑马";
    public static final String SCHOOL_NAME2;

    static {
        SCHOOL_NAME2 = "黑马";  // 静态代码块中赋值
    }

    public static void main(String[] args) {
        // final修饰基本类型
        final int a = 12;
        // a = 13;  // 编译错误：不能重新赋值

        // final修饰引用类型
        final ArrayList<String> list = new ArrayList<>();
        list.add("张三");  // 可以修改对象内容
        list.add("李四");
        // list = new ArrayList<>();  // 编译错误：不能改变引用

        System.out.println(SCHOOL_NAME);
        System.out.println(list);
    }
}
```

**运行结果：**
```
黑马
[张三, 李四]
```

**关键点：**
- 常量命名使用大写字母和下划线
- final修饰引用类型时，引用不能改变，但对象内容可以改变
- 常量可以在声明时赋值或在静态代码块中赋值

## 🎭 抽象类 (Abstract Classes)

### 1. 抽象类基础 (d5_abstract)

**核心概念：**
- 使用abstract关键字修饰的类
- 不能创建对象，仅作为特殊的父类
- 可以包含抽象方法和普通方法

**完整可运行代码：d5_abstract\Animals.java + Cat.java + Dog.java + Test.java**
```java
// Animals.java
package d5_abstract;

public abstract class Animals {
    private String name;

    public Animals() {}

    public Animals(String name) {
        this.name = name;
    }

    // 抽象方法：只有方法签名，没有方法体
    public abstract void cry();

    // 普通方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

// Cat.java
package d5_abstract;

public class Cat extends Animals {
    @Override
    public void cry() {
        System.out.println("猫咪叫");
    }
}

// Test.java
package d5_abstract;

public class Test {
    public static void main(String[] args) {
        // Animals a = new Animals();  // 编译错误：不能实例化抽象类

        Animals a1 = new Cat();
        a1.cry();

        Animals a2 = new Dog();
        a2.cry();
    }
}
```

**运行结果：**
```
猫咪叫
狗汪叫
```

**关键点：**
- 抽象类不能实例化，但可以作为父类引用
- 子类必须实现所有抽象方法，否则子类也必须声明为抽象类
- 抽象类的好处：强制子类重写，更好地支持多态

### 2. 抽象类的实际应用 (d6_abstract_demo)

**核心概念：**
- 抽象类用于定义模板和规范
- 强制子类实现特定方法
- 提供代码复用和统一接口

**完整可运行代码：d6_abstract_demo\Animals.java + Cat.java + Dog.java + Test.java**
```java
// Animals.java
package d6_abstract_demo;

public abstract class Animals {
    private String name;

    public Animals(String name) {
        this.name = name;
    }

    // 抽象类的好处：方法体无意义可以不写，强制子类重写 -> 更好的支持多态
    public abstract void cry();

    public String getName() {
        return name;
    }
}

// Test.java
package d6_abstract_demo;

public class Test {
    public static void main(String[] args) {
        Animals a1 = new Cat("波斯猫");
        a1.cry();
        System.out.println(a1.getName());

        Animals a2 = new Dog("金毛");
        a2.cry();
        System.out.println(a2.getName());
    }
}
```

**运行结果：**
```
猫咪叫
波斯猫
狗汪叫
金毛
```

### 3. 模板方法设计模式 (d7_abstract_demo2)

**核心概念：**
- 定义算法的骨架，具体步骤延迟到子类实现
- 使用final修饰模板方法，防止被重写
- 体现"控制反转"的设计思想

**完整可运行代码：d7_abstract_demo2\People.java + Student.java + Teacher.java + Test.java**
```java
// People.java
package d7_abstract_demo2;

public abstract class People {
    // final 不允许重写模板
    public final void write() {
        System.out.println("标题");
        System.out.println("1");
        writeMain();  // 调用抽象方法，由子类实现
        System.out.println("结尾");
    }

    // 抽象方法，由子类具体实现
    public abstract void writeMain();
}

// Student.java
package d7_abstract_demo2;

public class Student extends People {
    @Override
    public void writeMain() {
        System.out.println("正文：学生");
        System.out.println("好好学习，天天向上");
    }
}

// Teacher.java
package d7_abstract_demo2;

public class Teacher extends People {
    @Override
    public void writeMain() {
        System.out.println("正文：老师");
        System.out.println("下课别走");
    }
}

// Test.java
package d7_abstract_demo2;

public class Test {
    public static void main(String[] args) {
        // 写作文的步骤和架构是统一的
        // 1.写标题
        // 2.写正文
        // 3.统一的结束
        Student s1 = new Student();
        s1.write();

        System.out.println("----------");

        Teacher t1 = new Teacher();
        t1.write();
    }
}
```

**运行结果：**
```
标题
1
正文：学生
好好学习，天天向上
结尾
----------
标题
1
正文：老师
下课别走
结尾
```

**关键点：**
- 模板方法定义算法框架，抽象方法让子类实现具体步骤
- final确保模板方法不被重写，保持算法结构稳定
- 实现了代码复用和扩展性的平衡

## 🔌 接口 (Interface) 编程

### 1. 接口基础概念 (d8_interface)

**核心概念：**
- 使用interface关键字定义
- 接口不能创建对象，用来被类实现
- 一个类可以实现多个接口，弥补单继承不足

**完整可运行代码：d8_interface\A.java + B.java + BImpl.java + Test.java**
```java
// A.java
package d8_interface;

public interface A {
    // 1.常量（默认public static final修饰）
    String NAME = "黑马";

    // 2.抽象方法（默认public abstract修饰）
    void run();
    void go();
}

// B.java
package d8_interface;

public interface B {
    void eat();
}

// BImpl.java
package d8_interface;

public class BImpl implements A, B {
    // 实现类必须实现所有的接口，必须重写完全部接口方法
    // 否则实现类应该定义成抽象的

    @Override
    public void run() {
        System.out.println("跑步");
    }

    @Override
    public void go() {
        System.out.println("出发");
    }

    @Override
    public void eat() {
        System.out.println("吃饭");
    }
}

// Test.java
package d8_interface;

public class Test {
    public static void main(String[] args) {
        BImpl b = new BImpl();
        b.run();
        b.go();
        b.eat();

        System.out.println(A.NAME);  // 访问接口常量
    }
}
```

**运行结果：**
```
跑步
出发
吃饭
黑马
```

**关键点：**
- 接口中的变量默认是public static final修饰的常量
- 接口中的方法默认是public abstract修饰的抽象方法
- 实现类必须实现所有接口方法

### 2. 接口的多实现特性 (d9_interface2)

**核心概念：**
- 一个类可以实现多个接口
- 接口让对象拥有更多角色和能力
- 面向接口编程实现解耦合

**完整可运行代码：d9_interface2\People.java + Driver.java + Doctor.java + Student.java + Teacher.java + Test.java**
```java
// People.java
package d9_interface2;

public class People {
    private String name;

    public People(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}

// Driver.java
package d9_interface2;

public interface Driver {
    void drive();
}

// Doctor.java
package d9_interface2;

public interface Doctor {
    void cure();
}

// Student.java
package d9_interface2;

public class Student extends People implements Driver, Doctor {
    public Student(String name) {
        super(name);
    }

    @Override
    public void drive() {
        System.out.println(getName() + "会开车");
    }

    @Override
    public void cure() {
        System.out.println(getName() + "会治病");
    }
}

// Test.java
package d9_interface2;

public class Test {
    public static void main(String[] args) {
        // 接口让一个对象拥有更多角色更多能力
        People d0 = new Student("张三");
        Driver d = new Student("李四");  // 多态
        Doctor d1 = new Student("王五");

        d.drive();
        d1.cure();

        // 面向接口编程，更灵活的实现解耦合
        Driver d3 = new Teacher("赵六");
        d3.drive();
    }
}
```

**运行结果：**
```
李四会开车
王五会治病
赵六会开车
```

**关键点：**
- 接口实现了多重继承的效果
- 同一个对象可以有多种类型的引用
- 面向接口编程提高了代码的灵活性

### 3. 接口的实际应用案例 (d10_interface_demo)

**核心概念：**
- 面向接口编程的实际应用
- 通过接口实现业务逻辑的解耦合
- 不同实现策略的灵活切换

**完整可运行代码：d10_interface_demo\Student.java + ClassDate.java + ClassDateImpl1.java + ClassDateImpl2.java + Test.java**
```java
// Student.java
package d10_interface_demo;

public class Student {
    private String name;
    private char sex;
    private double score;

    public Student() {}

    public Student(String name, char sex, double score) {
        this.name = name;
        this.sex = sex;
        this.score = score;
    }

    // getter和setter方法
    public String getName() { return name; }
    public char getSex() { return sex; }
    public double getScore() { return score; }

    @Override
    public String toString() {
        return "Student{name='" + name + "', sex=" + sex + ", score=" + score + '}';
    }
}

// ClassDate.java
package d10_interface_demo;

public interface ClassDate {
    void printAllStudentInfo();
    void printAllStudentAverageScore();
}

// ClassDateImpl2.java
package d10_interface_demo;

import java.util.ArrayList;

public class ClassDateImpl2 implements ClassDate {
    private ArrayList<Student> students;

    public ClassDateImpl2(ArrayList<Student> students) {
        this.students = students;
    }

    @Override
    public void printAllStudentInfo() {
        System.out.println("=== 输出所有学生信息 ===");
        int maleCount = 0;
        for (int i = 0; i < students.size(); i++) {
            if (students.get(i).getSex() == '男') maleCount++;
            System.out.println(students.get(i).toString());
        }
        System.out.println("男生人数：" + maleCount);
        System.out.println("女生人数：" + (students.size() - maleCount));
    }

    @Override
    public void printAllStudentAverageScore() {
        double sum = 0;
        for (Student student : students) {
            sum += student.getScore();
        }
        System.out.println("班级平均分：" + (sum / students.size()));
    }
}

// Test.java
package d10_interface_demo;

import java.util.ArrayList;

public class Test {
    public static void main(String[] args) {
        // 1.每个学生是一个对象，需要先定义学生类，用于创建学生对象，封装学生数据

        // 2.定义接口 ClassDate
        ArrayList<Student> students = new ArrayList<>();
        students.add(new Student("张三", '男', 95));
        students.add(new Student("钟灵", '女', 75));
        students.add(new Student("李四", '男', 50));
        students.add(new Student("公主", '女', 98));
        students.add(new Student("虚竹", '男', 90));

        // 3.定义两套实现类，来分别处理，以便解耦合
        ClassDate classDate2 = new ClassDateImpl2(students);
        classDate2.printAllStudentInfo();
        classDate2.printAllStudentAverageScore();
    }
}
```

**运行结果：**
```
=== 输出所有学生信息 ===
Student{name='张三', sex=男, score=95.0}
Student{name='钟灵', sex=女, score=75.0}
Student{name='李四', sex=男, score=50.0}
Student{name='公主', sex=女, score=98.0}
Student{name='虚竹', sex=男, score=90.0}
男生人数：3
女生人数：2
班级平均分：81.6
```

**关键点：**
- 通过接口定义业务规范，不同实现类提供不同策略
- 面向接口编程实现了业务逻辑与具体实现的解耦
- 便于扩展和维护，符合开闭原则

### 4. 接口的多继承 (d11_interface_extends)

**核心概念：**
- 接口与接口之间可以多继承
- 一个接口可以同时继承多个接口
- 实现类只需实现一个接口就相当于实现了多个接口

**完整可运行代码：d11_interface_extends\B.java + C.java + A.java + D.java + Test.java**
```java
// B.java
package d11_interface_extends;

public interface B {
    void b();
}

// C.java
package d11_interface_extends;

public interface C {
    void c();
}

// A.java
package d11_interface_extends;

// 接口和接口是多继承的，一个接口可以同时继承多个接口
public interface A extends B, C {
    void a();
}

// D.java
package d11_interface_extends;

public class D implements A {
    @Override
    public void a() {
        System.out.println("实现a方法");
    }

    @Override
    public void b() {
        System.out.println("实现b方法");
    }

    @Override
    public void c() {
        System.out.println("实现c方法");
    }
}

// Test.java
package d11_interface_extends;

public class Test {
    public static void main(String[] args) {
        //接口的多继承可以让实现类只实现一个接口，相当于实现了很多接口
        D d = new D();
        d.a();
        d.b();
        d.c();
    }
}
```

**运行结果：**
```
实现a方法
实现b方法
实现c方法
```

**关键点：**
- 接口支持多继承，类只支持单继承
- 接口多继承简化了实现类的工作
- 提供了更灵活的设计方式

## 🚀 JDK8接口新特性

### 1. 接口新增方法类型 (d12_interrface_jdk8)

**核心概念：**
- JDK8为接口新增了默认方法、静态方法和私有方法
- 解决了接口演进的问题
- 提供了更强大的接口功能

**完整可运行代码：d12_interrface_jdk8\A.java + B.java + Test.java**
```java
// A.java
package d12_interrface_jdk8;

public interface A {
    // 默认方法，实例方法，必须用default修饰
    // 默认用public修饰
    public default void run() {
        go();
        System.out.println("run");
    }

    // 私有方法(私有的实例方法) JDK9才有的
    // 只能当前接口内部的默认方法或者私有方法来调用
    private void go() {
        System.out.println("go go go");
    }

    // 静态方法
    // 默认会用public修饰
    // 接口的静态方法必须用接口名本身调用
    static void inAddr() {
        System.out.println("inAddr go go");
    }
}

// B.java
package d12_interrface_jdk8;

public class B implements A {
    // 可以选择重写默认方法，也可以不重写
}

// Test.java
package d12_interrface_jdk8;

public class Test {
    public static void main(String[] args) {
        B b = new B();
        b.run();  // 调用默认方法

        A.inAddr();  // 调用静态方法
    }
}
```

**运行结果：**
```
go go go
run
inAddr go go
```

**关键点：**
- 默认方法使用default修饰，实现类可以选择重写
- 静态方法必须用接口名调用
- 私有方法只能在接口内部使用，用于代码复用

### 2. 接口冲突解决 (d13_interface)

**核心概念：**
- 多接口实现时可能出现方法冲突
- Java提供了明确的冲突解决规则
- 确保程序的确定性和可预测性

**完整可运行代码：d13_interface\A3.java + B3.java + C3.java + Test.java**
```java
// A3.java
package d13_interface;

public interface A3 {
    default void run() {
        System.out.println("A3 run run");
    }
}

// B3.java
package d13_interface;

public interface B3 {
    default void run() {
        System.out.println("B3 run run");
    }
}

// C3.java
package d13_interface;

public class C3 implements A3, B3 {
    // 一个类实现多个接口，多个接口中存在同名的默认方法，这个类必须重写该方法
    @Override
    public void run() {
        System.out.println("C3 run run");
        A3.super.run();  // 调用A3的默认方法
        B3.super.run();  // 调用B3的默认方法
    }
}

// Test.java
package d13_interface;

public class Test {
    public static void main(String[] args) {
        C3 c3 = new C3();
        c3.run();
    }
}
```

**运行结果：**
```
C3 run run
A3 run run
B3 run run
```

**冲突解决规则：**
1. **方法签名冲突**：返回值类型不同时，不支持多继承
2. **类继承与接口实现冲突**：优先使用父类方法
3. **多接口默认方法冲突**：必须重写方法解决冲突

## 📊 核心概念对比总结

### 抽象类与接口对比

| 特性 | 抽象类 | 接口 |
|------|--------|------|
| **关键字** | abstract class | interface |
| **继承关系** | extends（单继承） | implements（多实现） |
| **成员变量** | 可以有各种类型的成员变量 | 只能有常量（public static final） |
| **方法类型** | 抽象方法、普通方法、构造器 | 抽象方法、默认方法、静态方法、私有方法 |
| **访问修饰符** | 可以有各种访问修饰符 | 方法默认public，变量默认public static final |
| **构造器** | 可以有构造器 | 不能有构造器 |
| **实例化** | 不能直接创建对象 | 不能直接创建对象 |

### 使用场景选择

**选择抽象类的情况：**
- 需要在父类中提供一些默认实现
- 需要定义非静态或非final的字段
- 需要访问修饰符不是public的方法
- 类之间有明确的"is-a"关系

**选择接口的情况：**
- 需要多重继承的效果
- 定义一组相关的方法规范
- 不同类之间需要相同的行为契约
- 实现松耦合的设计

## 🎯 学习成果

### 核心技能掌握
- ✅ 多态机制的使用和应用场景
- ✅ Final关键字的作用机制
- ✅ 抽象类的设计原理和实现规范
- ✅ 接口编程和面向接口设计
- ✅ 模板方法设计模式的应用
- ✅ JDK8接口新特性的使用

### 面向对象三大特性进阶
1. **封装**：通过访问修饰符控制成员访问权限
2. **继承**：实现代码复用和层次结构（day01学习重点）
3. **多态**：同一方法在不同对象上产生不同行为（本次学习重点）

### 后续学习方向
为后续学习Java高级特性、设计模式和框架技术奠定了坚实基础。
