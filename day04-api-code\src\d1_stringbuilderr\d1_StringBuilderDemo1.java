package d1_stringbuilderr;

import java.sql.SQLOutput;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 20:20
 **/
public class d1_StringBuilderDemo1 {
    public static void main(String[] args) {
        //目标学会用stringbuilder
        //1.创建对象
        StringBuilder sb = new StringBuilder();   // sb = ""
        StringBuilder sb2 = new StringBuilder("黑马");  //sb2 = "黑马"


        //2.拼接内容
        //append方法
        sb2.append("java").append("Java").append(666).append(true);

        System.out.println(sb2);

        sb.append("黑马");
        sb.append("java");
        sb.append(666);
        sb.append(true);
        System.out.println(sb);


        //3.反转内容
        sb.reverse();
        System.out.println(sb);

        //4.长度
        System.out.println(sb.length());

        //5.把Stringbuilder 对象转换成String
        //stringbuilder 是拼接字符串的手段
        //string 才是开发中的目的
        String result = sb.toString();
        System.out.println(result);



    }
}