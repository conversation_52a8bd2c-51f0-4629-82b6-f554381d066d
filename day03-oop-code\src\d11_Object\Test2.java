package d11_Object;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/24 - 19:00
 **/
public class Test2 {
    public static void main(String[] args) {
        A a = new A("张三",18);
        A a1 = new A("张三",18);

        System.out.println(a.toString());
        System.out.println(a.hashCode());

    }
}
class A{
    private String name;
    private int age;
    public A(){
    }

    public A(String name,int age){
        this.name = name;
        this.age = age;
    }
}