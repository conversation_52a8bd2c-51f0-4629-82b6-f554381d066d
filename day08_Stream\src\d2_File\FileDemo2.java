package d2_File;

import java.io.File;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class FileDemo2 {
    public static void main(String[] args) {
        //1.创建文件对象，指向某个文件
        File f  = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource\\image_01.png");

        //exists 判断当前文件对象，对应的文件路径是否存在 存在返回true
        System.out.println(f.exists());

        // isfile 判断当前对象是否是文件，是为true
        System.out.println(f.isFile());

        //4. isDirectary  判断当前文件对象指代的是否是文件夹   是为true 否则为false
        System.out.println( f.isDirectory());

        //5.getName  获取文件的名称 包含后缀
        System.out.println(f.getName());

        //6.length 获取文件大小，返回字节数
        System.out.println(f.length());

        //7.lastModified 获取文件最后折修改时间
        long l = f.lastModified();
        /*//1.使用 SimpleDateFormat
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(l);*/

        //方法二 使用LocalDateTime
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss EEE a");
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(l), ZoneId.systemDefault());
        String date = dateTime.format(dtf);

        System.out.println(date);

        //8.getpath
        //绝对路径
        System.out.println(f.getParent());

        //9. getAbsolutePath
        System.out.println(f.getAbsolutePath());

        File file9 = new File("");
        System.out.println(file9.getAbsoluteFile());
    }
}
