package d5_map_reavesal;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

public class MapDemo3 {
    public static void main(String[] args) {
        //目标：掌握map集合的遍历方式
        Map<String,Double> map = new HashMap<>();

        map.put("小龙女",66.6);
        map.put("孙悟空",88.8);
        map.put("蜘蛛精",77.7);
        map.put("牛魔王",99.9);

        System.out.println(map);

        map.forEach(new BiConsumer<String, Double>() {
            @Override
            public void accept(String key, Double value) {
                System.out.println(key + "    " + value);
            }
        });

        map.forEach((key,value) -> System.out.println(key+"    "+ value));
    }
}
