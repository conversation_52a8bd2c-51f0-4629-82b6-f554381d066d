package d2_xml;

import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import java.util.List;

public class Dom4JTest1 {
    public static void main(String[] args) throws Exception {
        // Parse XML file using Dom4J framework
        // 1. 创建一个SAXReader解析器对象
        SAXReader saxReader = new SAXReader();

        // 2. 把xml文件读取成一个document文档对象
        Document document = saxReader.read("day11-special-file-log-code/src/contact.xml");

        // 3. 文档对象中包含了xml对象的全部，提供了方法获取数据
        Element rootElement = document.getRootElement();
        System.out.println(rootElement.getName());

        //4.提取子元素对象
        List<Element> sonEle = rootElement.elements();
        //指定某个子元素
        //List<Element> sonEle = rootElement.elements("user");


        for(Element ele:sonEle){
            System.out.println(ele.getName());
        }
        Element userEle = rootElement.element("contact"); //默认拿第一个
        System.out.println(userEle.getName());
        System.out.println(userEle.elementText("name"));  //文本值

        //5.提取子元素的属性对象
        Attribute attr = userEle.attribute("id");
        System.out.print(attr.getName()+":"+attr.getValue()+"\n");
        //直接拿属性值
        System.out.println("id:"+userEle.attributeValue("id"));

        //6.文本值  通过父元素拿到子元素文本值
        System.out.println(userEle.elementText("name"));
        System.out.println(userEle.elementTextTrim("name"));  //去掉前后空格

        //先拿到元素对象，再提取其文本值
        Element emailEle = userEle.element("email");
        System.out.println(emailEle.getText());
        System.out.println(emailEle.getTextTrim());
    }
}