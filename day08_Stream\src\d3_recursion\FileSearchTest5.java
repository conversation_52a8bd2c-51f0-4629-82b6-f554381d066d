package d3_recursion;

import java.io.File;

public class FileSearchTest5 {
    public static void main(String[] args) {
        File dir = new File("D:\\Program Files\\Tencent");
        searchFile(dir,"QQ.exe");
    }
    public static void searchFile(File dir,String fileName){
        if(dir == null || !dir.exists() || dir.isFile()) return ;

        //提取所有一级文件
        File[] file = dir.listFiles();

        //判断这个文件是否可以提取
        if(file == null) return ;

        for(File f : file){
            //判断是否是目录  是则继续深入   不是则判断是否是目标文件
            if(f.isDirectory()){
                searchFile(f,fileName);
            }

            if(f.isFile()&&f.getName().contains(fileName)){
                System.out.println(f.getAbsolutePath());
            }
        }
    }
}
