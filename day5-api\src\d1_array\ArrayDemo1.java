package d1_array;

import java.util.Arrays;
import java.util.function.IntToDoubleFunction;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/25 - 16:06
 **/
public class ArrayDemo1 {
    public static void main(String[] args) {
        int[] arr = {11,55,33,22,98};
        String result = Arrays.toString(arr);
        System.out.println(result);

        //2.拷贝数组的内容到一个新数组，并返回新数组
        // public static 类型[] copyOfRange(类型【】原数组，开始索引，结束索引)
        int [] arr2=Arrays.copyOfRange(arr,1,4);//包前不包后
        System.out.println(Arrays.toString(arr2));

        //3.给数组扩容
        //public static 类型[] copyOf(类型【】原数组，新数组长度)
        int [] arr3 = Arrays.copyOf(arr,10);
        System.out.println(Arrays.toString(arr3));

        //4.数组排序
        //public static void sort(类型【】数组)
        //升序排序  快排
        Arrays.sort(arr);
        System.out.println(Arrays.toString(arr));




        //5.修改数组中每个数据，再存入
        double[] score={99.5,90,59.5,78,98,55};
        //需求给每个分数都加10分
        Arrays.setAll(score, new IntToDoubleFunction() {
            @Override
            //index 索引位置
            public double applyAsDouble(int index) {
                return score[index]+10;
            }
        });
        System.out.println(Arrays.toString(score));



    }
}