package d1_properties;


import java.io.FileReader;
import java.util.Properties;
import java.util.Set;

public class PropertiesDemo1 {
    public static void main(String[] args) throws Exception {
        //本质map
        //创建属性集对象，代码一个属性文件
        Properties properties = new Properties();
        System.out.println(properties);

        //2.加载属性文件到属性集合对象中

        properties.load(new FileReader("day11-special-file-log-code\\src\\user.properties"));

        System.out.println(properties);

        //根据键取值
        System.out.println(properties.get("admin"));
        System.out.println(properties.getProperty("admin"));

        //取键
        Set<String> keys = properties.stringPropertyNames();
        for(String key : keys){
            System.out.println(key + ":" + properties.getProperty(key));
        }

        //3.遍历数据
        properties.forEach((k,v)->{
            System.out.println(k+":"+v);
        });
    }
}
