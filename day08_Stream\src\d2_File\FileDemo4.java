package d2_File;

import java.io.File;

public class FileDemo4 {
    public static void main(String[] args) {
        //文件的遍历
        File file = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource");

        //list    获取当前目录下的所有一级文件名称到一个字符数组中去返回
//        String[] name = file.list();
//        for(String s : name){
//            System.out.println(s);
//        }

        //listfiles 获取当前目录下所有的一级对象 到一个文件对象数组中去返回 （重点）
        //主调是文件，或路径不存在时，返回 null
        //主调是空文件夹时，返回长度为 0 的数组
        //当主调是一个有内容的文件夹时，将里面所有一级文件和文件夹的路径放在file数组中返回
        //主调是文件时，里面有隐藏文件时，将里面所有文件夹的路径放在file数组中返回，包含隐藏文件
        //主调是文件时，但没有权限访问该文件的夹时，返回null

        File f2 = new File("D:\\Code\\ST-Java\\Java-01\\JavaSEProMax\\Resource");

        File[] files = f2.listFiles();
        System.out.println(files);
        for(File f : files){
            System.out.println(f);
        }



    }
}
