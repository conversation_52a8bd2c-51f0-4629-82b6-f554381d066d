package d9_interface2;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/23 - 15:35
 **/
public class Test {
    public static void main(String[] args) {
        //目标：理解接口的好处
        //1.弥补继承的不足,接口让一个对象拥有更多角色更多能力
        People d0 = new Student();
        Driver d = new Student ();  //多态
        Doctor d1 = new Student ();

        //2.面向接口编程是软件开发中目前很流行的开发模式，能更灵活的实现解耦合
        Driver d3 = new Teacher();


    }
}