package d1_exception;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/28 - 19:54
 **/
public class ExceptionDemo3 {
    public static void main(String[] args) {

        //定位bug  作用返回值
        int age = 160;

        try {
            save(age);
        } catch (Exception e) {
            e.printStackTrace();   //打印异常信息
        }
    }
    public static void save(int age) throws AgeIllegaRunTimeException {
        //throw 方法内部使用的，创建异常并从此跳出
        //throws 方法上，抛出方法内部的异常给调用者
        if(age <= 0 || age > 150){
            //这个年龄非法！创建异常对象并立即抛出   并异常抛出给虚拟机
            throw new AgeIllegaRunTimeException("年龄非法");
        }
    }
}