package d4_static_util;

import java.util.Random;

/**
 * @Description: 工具类
 * @Author: Alhz
 * @Date: 2025/7/21 - 19:37
 **/

/*
*
* */
public class IteimaUtil {

    //工具类没有创建对象的需求，需要私有构建器
    private IteimaUtil() {

    }

    public static String createCode(int cnt){
        String data="ABCDEFGHIJKLMNOPQRSRUVWXYZabcdefghijklmnopqrstuvwxyz123456789";
        String code="";
        Random random=new Random();
        for(int i=0;i<cnt;i++){
            int index=random.nextInt(data.length());
            code+=data.charAt(index);
        }
        return code;
    }
}