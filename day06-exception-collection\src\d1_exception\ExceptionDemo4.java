package d1_exception;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description:
 * @Author: Alhz
 * @Date: 2025/7/29 - 09:24
 **/
public class ExceptionDemo4 {
    public static void main(String[] args) {
        //异常的处理机制

        System.out.println("===开始===");

        //1.异常的处理机制
        //统一处理异常
        try {
            parseDate("2024-03-19 13:20:31");
        } catch (Exception e) {
           e.printStackTrace();
        }

        System.out.println("===结束===");
    }

    public static void parseDate(String s) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date d = sdf.parse(s);

        System.out.println(d);

        InputStream is = new FileInputStream("a.txt");
    }
}