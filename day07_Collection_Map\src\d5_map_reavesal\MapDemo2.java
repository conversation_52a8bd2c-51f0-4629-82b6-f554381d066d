package d5_map_reavesal;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class MapDemo2 {
    public static void main(String[] args) {
        //目标：掌握map集合的遍历方式
        Map<String,Double> map = new HashMap<>();

        map.put("小龙女",66.6);
        map.put("孙悟空",88.8);
        map.put("蜘蛛精",77.7);
        map.put("牛魔王",99.9);

        System.out.println(map);

        //map.entry
        Set<Map.Entry<String,Double>> entrySet = map.entrySet();

        for(Map.Entry<String,Double> entry:entrySet){
            System.out.println(entry.getKey() + "    " + entry.getValue());
        }

    }
}
