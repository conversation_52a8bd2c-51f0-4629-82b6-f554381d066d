# Java面向对象编程 - Static与继承

## 📋 目录
- [Static 静态特性](#static-静态特性)
  - [静态变量](#静态变量)
  - [静态方法](#静态方法)
  - [代码块](#代码块)
  - [单例设计模式](#单例设计模式)
- [继承机制](#继承机制)
  - [继承基础](#继承基础)
  - [访问权限修饰符](#访问权限修饰符)
  - [方法重写](#方法重写)
  - [子类构造器](#子类构造器)

---

## 🔧 Static 静态特性

### 静态变量

**定义：** 可以修饰成员变量或成员方法

**分类：** 按照有无static修饰，分为两种

#### 1. 类变量（静态变量）
- **特点：** 有static修饰，属于类，在计算机里只有一份，会被类的全部对象共享
- **用法：** `类名.类变量` 或 `对象名.类变量`（推荐前者）

#### 2. 实例变量（对象变量）
- **特点：** 无static修饰，属于每个对象的
- **用法：** 通过对象访问

**💡 使用场景：** 如果某个数据只需要一份，且希望能够共享（访问、修改），则用类变量来定义

---

### 静态方法

**应用场景：** 在进行工具类的设计中，不需要实例方法，只需要通过类名调用即可。

**⚠️ 重要提示：** 工具类没有创建对象的需求，建议工具类的构造器私有。

#### Static方法的访问规则

**核心规则：**
1. **静态方法只能直接访问静态成员**
2. **实例方法可以访问所有成员（静态+实例）**
3. **静态方法中不能使用this关键字**

**💡 重要特性：** 静态方法属于类，不属于对象，因此不能被重写，不参与多态机制

![静态方法示意图](D:\Code\ST-Java\Java-01\JavaSEProMax\MD\resouses\d5d19e30-6640-11f0-8e60-e5120afd19a4.webp)

---

### 代码块

**定义：** 代码块是类的五大成分之一（成员变量、构造器、方法、代码块、内部类）

#### 静态代码块
- **执行时机：** 类加载时自动执行，由于类只会加载一次
- **作用：** 可以用来初始化静态变量的数据

#### 实例代码块
- **执行时机：** 每次创建对象时执行，并在构造器前执行
- **作用：** 可以用来初始化实例（对象）变量的数据

---

### 单例设计模式

#### 饿汉式单例
- **特点：** 拿对象时，对象早就创建好了
- **优点：** 线程安全
- **缺点：** 可能造成内存浪费

#### 懒汉式单例
- **特点：** 拿对象时，才开始创建对象
- **优点：** 节省内存
- **缺点：** 需要考虑线程安全问题

---

## 🏗️ 继承机制

### 继承基础

**关键字：** `extends`

**语法：**
```java
public class B extends A {
    /*  A类称为父类
     *  B类称为子类
     */
}
```

#### 继承特点
- **子类继承：** 子类能继承父类的非私有成员（成员变量、成员方法）
- **对象构成：** 子类的对象是由子类、父类共同完成的

#### 继承的好处
- **代码复用：** 减少重复代码的编写
- **层次结构：** 建立清晰的类关系
- **扩展性：** 便于功能扩展和维护

---

### 访问权限修饰符

| 修饰符 | 本类 | 同包 | 子类 | 任意位置 |
|--------|------|------|------|----------|
| `private` | ✓ | ✗ | ✗ | ✗ |
| 缺省(默认) | ✓ | ✓ | ✗ | ✗ |
| `protected` | ✓ | ✓ | ✓ | ✗ |
| `public` | ✓ | ✓ | ✓ | ✓ |

**权限大小关系：**
```
private < 缺省 < protected < public
```

---

### 单继承和Object类

#### 为什么Java不支持多继承？
当多个父类存在相同的方法时，会出现继承哪个的问题，分不清多个父类的方法。

#### Object类
**定义：** Object类是Java所有类的祖宗类。

**特点：** 所有Java类都直接或间接继承自Object类

---

### 方法重写

**定义：** 子类觉得父类中的某个方法不好用，或无法满足自己的需求时，子类可以重新定义一个方法名称、参数列表一样的方法，来覆盖父类的方法。

#### 重写规则
1. **注解：** 使用`@Override`注解
2. **权限：** 子类重写父类方法时，访问权限必须大于或等于父类该方法的权限
3. **返回值：** 重写的方法返回值类型，必须与被重写方法的返回值类型一样，或者是其子类
4. **限制：** 私有方法与静态方法不能被重写

#### 方法重写与多态的关系
- **技术基础：** 方法重写是实现多态的技术基础
- **运行机制：** 重写使得"编译看左边，运行看右边"成为可能
- **核心作用：** 同一方法调用在不同子类对象上产生不同行为

**⚠️ 重要限制：**
- **静态方法**：不能被重写，不参与多态
- **构造器**：不能被重写，不参与多态
- **final方法**：不能被重写，不参与多态

---

### 子类中访问其他成员的特点

#### 访问顺序（就近原则）
1. **先子类局部范围找**
2. **然后子类成员范围找**
3. **最后父类范围找**，如果父类范围没有找到就报错

**💡 提示：** `super`只能在子类中使用

---

### 子类构造器

#### 调用特点
- **默认行为：** 子类的全部构造器，都会先调用父类的构造器，再执行自己

#### 实现机制
- **默认情况：** 子类全部构造器的第一行代码都是`super()`（写不写都有），它会调用父类的无参构造器
- **特殊情况：** 如果父类没有无参构造器，子类构造器的第一行要手写`super(参数)`，指定去调用父类的有参构造器

#### 注意事项
- `super()`必须在构造器的第一行
- `this()`和`super()`不能同时出现
- 构造器调用顺序：父类构造器 → 子类构造器

---

## 📝 总结

### Static特性要点
- **静态变量：** 类级别共享，内存中只有一份
- **静态方法：** 通过类名调用，适用于工具类
- **代码块：** 静态代码块用于初始化静态资源，实例代码块用于初始化对象
- **单例模式：** 控制类的实例化，确保只有一个对象

### 继承机制要点
- **代码复用：** 子类继承父类的非私有成员
- **访问控制：** 通过修饰符控制成员的访问范围
- **方法重写：** 子类可以重新定义父类的方法
- **构造器链：** 子类构造器会先调用父类构造器

