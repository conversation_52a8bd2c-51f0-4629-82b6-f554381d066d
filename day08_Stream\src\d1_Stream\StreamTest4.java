package d1_Stream;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.Math.max;

public class StreamTest4 {
    public static void main(String[] args) {
        List<Movie> movies = new ArrayList<>();
        movies.add(new Movie("肖申克的救赎", 9.5, "阿米尔汗"));
        movies.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies.add(new Movie("阿甘正传", 7.5, "汤姆汉克斯"));


        //1.forEach
        movies.forEach(System.out::println);

        //count
        long count = movies.stream().count();
        System.out.println(count);

        //3.取最大值
        //避免空指异常
        //Optional<Movie> max =  movies1.stream().max((o1, o2) -> Double.compare(o2.getScore(),o1.getScore()));
        Movie max =  movies.stream().max((o1, o2) -> Double.compare(o2.getScore(),o1.getScore())).get();

        Movie min =  movies.stream().min((o1,o2) -> Double.compare(o2.getScore(),o1.getScore())).get();

        System.out.println(max);

        System.out.println(min);

        //Stream流是：方便操作集合/数组的手段；  集合和数组才是开发的目的。



        List<String> list = new ArrayList<>();

        list.add("张无忌");
        list.add("周芷若");
        list.add("赵敏");
        list.add("张强");
        list.add("张三丰");
        list.add("张三丰");

        //收集Stream流：把流中的数据恢复到集合或数组中
        //.collect(Collectors.toList)

        Stream<String> stream = list.stream();
        List<String> list1 = list.stream().filter(s -> s.startsWith("张")&&s.length()==3).collect(Collectors.toList());
        //jdk16支持
        // List<String> newList = list.stream().filter(s -> s.startsWith("张")&&s.length()==3).toList();

        System.out.println(list1);

        //收集到set集合中
        Stream<String> stream1 = list.stream();
        Set<String> set = stream1.collect(Collectors.toSet());
        System.out.println(set);


        //收集到数组中
        Stream<String> stream2 = list.stream();
        //只能采用object来接收
        Object[] string = stream2.filter(s -> s.startsWith("张")&&s.length()==3).toArray();
        System.out.println(Arrays.toString(string));


        //收到map集合
        List<Movie> movies1 = new ArrayList<>();
        movies1.add(new Movie("肖申克的救赎", 9.5, "阿米尔汗"));
        movies1.add(new Movie("二傻大闹宝莱坞", 8.5, "阿米尔汗2"));
        movies1.add(new Movie("二傻大闹宝莱坞", 19, "阿米尔汗2"));
        movies1.add(new Movie("阿甘正传", 7.5, "汤姆汉克斯"));

        //Duplicate key   不支持重复   map 需要去重后再操作          // v1 和 v2   中选一个  v2
        Map<String,Double> map = movies1.stream().limit(2).collect(Collectors.toMap(m1->m1.getName(),m2->m2.getScore() , (v1,v2) -> v2));

        System.out.println(map);


    }
}
